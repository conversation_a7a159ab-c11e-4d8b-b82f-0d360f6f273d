//
//  ThemeOptionView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/8/3.
//

import SwiftUI
import AutoInch

/// 主題選項組件
struct ThemeOptionView: View {
    let theme: ThemeMode
    let isSelected: Bool
    let canSelect: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: CGFloat(12).auto()) {
                // Radio button
                AppIconsSymbol.createView(
                    for: isSelected ? AppIcons.radioboxcheck : AppIcons.radiobox,
                    fontSize: CGFloat(44).auto(),
                    color: isSelected ? HexColor.themed(.mainColor) : HexColor.themed(.secondaryText)
                )
                .opacity(canSelect ? (isSelected ? 1.0 : 0.3) : 0.2)
                // .opacity(canSelect ? 1.0 : 0.3)
                .animation(.easeInOut(duration: 0.2), value: isSelected)

                // 顏色色票
                HStack(spacing: CGFloat(0).auto()) {
                    Rectangle()
                        .fill(getThemeColor(.primaryBackground))
                        .frame(width: CGFloat(16).auto(), height: CGFloat(16).auto())

                    Rectangle()
                        .fill(getThemeColor(.mainColor))
                        .frame(width: CGFloat(16).auto(), height: CGFloat(16).auto())
                }
                .cornerRadius(CGFloat(6).auto())
                .overlay(
                    RoundedRectangle(cornerRadius: CGFloat(6).auto())
                        .stroke(
                            isSelected ? getThemeColor(.mainColor) : HexColor.themed(.separator),
                            lineWidth: isSelected ? CGFloat(2).auto() : CGFloat(1).auto()
                        )
                )
                .opacity(canSelect ? 1.0 : 0.4)
                .scaleEffect(isSelected ? 1.1 : 1.0)
                .animation(.easeInOut(duration: 0.2), value: isSelected)

                // 主題名稱
                // Text(theme.displayName)
                //     .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                //     .foregroundColor(isSelected ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                //     .opacity(canSelect ? (isSelected ? 1.0 : 0.7) : 0.4)
                //     .animation(.easeInOut(duration: 0.2), value: isSelected)

                // Spacer()

                // Pro 標籤（如果需要）
                // if theme.isProTheme && !canSelect {
                //     Text("PRO")
                //         .font(.system(size: CGFloat(10).auto(), weight: .bold, design: .rounded))
                //         .foregroundColor(.white)
                //         .padding(.horizontal, CGFloat(6).auto())
                //         .padding(.vertical, CGFloat(2).auto())
                //         .background(
                //             RoundedRectangle(cornerRadius: CGFloat(4).auto())
                //                 .fill(HexColor.themed(.premium))
                //         )
                // }

                // 選中指示器
                // if isSelected {
                //     AppIconsSymbol.createView(
                //         for: AppIcons.check,
                //         fontSize: CGFloat(20).auto(),
                //         color: getThemeColor(.mainColor)
                //     )
                //     .animation(.easeInOut(duration: 0.2), value: isSelected)
                // }
            }
            .offset(x: CGFloat(-10).auto())
            // .padding(.horizontal, CGFloat(4).auto())
            // .padding(.vertical, CGFloat(8).auto())
        }
    }

    /// 獲取指定主題的顏色
    private func getThemeColor(_ colorTheme: HexColor.ColorTheme) -> Color {
        let colorMap = getThemeColorMap()
        guard let hexValue = colorMap[colorTheme] else {
            return HexColor.themed(colorTheme) // 回退到當前主題
        }
        return HexColor.color(hexValue)
    }

    /// 獲取主題顏色映射
    private func getThemeColorMap() -> [HexColor.ColorTheme: String] {
        switch theme {
        case .light:
            return [
                .primaryBackground: "F9F9F9",
                .mainColor: "222222"
            ]
        case .dark:
            return [
                .primaryBackground: "222222",
                .mainColor: "F9F9F9"
            ]
        case .muji:
            return [
                .primaryBackground: "FFFFFF",
                .mainColor: "7F0019"
            ]
        case .ana:
            return [
                .primaryBackground: "FFFFFF",
                .mainColor: "001F3F"
            ]
        }
    }
}

// MARK: - 預覽
#Preview {
    VStack(spacing: CGFloat(12).auto()) {
        Text("主題選擇器預覽")
            .font(.system(size: CGFloat(20).auto(), weight: .bold, design: .rounded))
            .foregroundColor(HexColor.themed(.primaryText))
            .padding(.bottom, CGFloat(8).auto())

        VStack(spacing: CGFloat(8).auto()) {
            ThemeOptionView(
                theme: .light,
                isSelected: true,
                canSelect: true,
                onTap: {}
            )

            ThemeOptionView(
                theme: .dark,
                isSelected: false,
                canSelect: true,
                onTap: {}
            )

            ThemeOptionView(
                theme: .muji,
                isSelected: false,
                canSelect: false,
                onTap: {}
            )

            ThemeOptionView(
                theme: .ana,
                isSelected: false,
                canSelect: false,
                onTap: {}
            )
        }
        .padding(CGFloat(16).auto())
        .background(
            RoundedRectangle(cornerRadius: CGFloat(12).auto())
                .fill(HexColor.themed(.secondaryBackground))
        )
    }
    .padding()
    .background(HexColor.themed(.primaryBackground))
}
