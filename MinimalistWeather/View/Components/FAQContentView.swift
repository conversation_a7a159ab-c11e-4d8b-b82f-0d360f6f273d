//
//  FAQContentView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

/// 共用的 FAQ 內容元件
struct FAQContentView: View {
    // FAQ 服務
    @StateObject private var faqService = FAQService.shared
    
    // 動態展開狀態字典
    @State private var expandedStates: [UUID: Bool] = [:]
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(12).auto()) {
            if faqService.isLoading {
                // 載入中狀態
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("loading_faq".localized)
                        .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.secondaryText))
                }
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.vertical, CGFloat(20).auto())
            } else if let errorMessage = faqService.errorMessage {
                // 錯誤狀態
                VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
                    Text("error_loading_faq".localized)
                        .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.primaryText))

                    Text(errorMessage)
                        .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(HexColor.themed(.secondaryText))

                    Button(action: {
                        faqService.loadCurrentLanguageFAQ()
                    }) {
                        Text("retry".localized)
                            .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                            .padding(.horizontal, CGFloat(16).auto())
                            .padding(.vertical, CGFloat(8).auto())
                            .background(
                                RoundedRectangle(cornerRadius: CGFloat(8).auto())
                                    .stroke(HexColor.themed(.primaryText), lineWidth: 1)
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.vertical, CGFloat(20).auto())
            } else {
                // FAQ 內容
                ForEach(faqService.faqItems) { faqItem in
                    DisclosureGroup(
                        isExpanded: Binding(
                            get: { expandedStates[faqItem.id] ?? false },
                            set: { expandedStates[faqItem.id] = $0 }
                        ),
                        content: {
                            VStack(alignment: .leading, spacing: CGFloat(0).auto()) {
                                Text(faqItem.body)
                                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                                    .multilineTextAlignment(.leading)
                                    .lineSpacing(6) // 行與行之間的間距（不是整體行高）
                            }
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.top, CGFloat(12).auto())
                        },
                        label: {
                            Button(action: {
                                withAnimation {
                                    expandedStates[faqItem.id] = !(expandedStates[faqItem.id] ?? false)
                                }
                            }) {
                                Text(faqItem.title)
                                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.primaryText))
                                    .multilineTextAlignment(.leading)
                                    .padding(.top, CGFloat(8).auto())
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    )
                    .accentColor(HexColor.themed(.primaryText))
                }
            }
        }
        .onAppear {
            // 載入當前語言的 FAQ 資料
            faqService.loadCurrentLanguageFAQ()
        }
    }
}

#Preview {
    FAQContentView()
        .padding()
}
