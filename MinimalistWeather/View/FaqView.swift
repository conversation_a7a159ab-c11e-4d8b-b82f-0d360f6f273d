//
//  WeatherSourceInfoView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/7/19.
//

import SwiftUI
import AutoInch

// MARK: - 天氣來源詳細資訊視圖
struct FaqView: View {
    @Environment(\.presentationMode) var presentationMode
    let onDismiss: () -> Void

    init(onDismiss: @escaping () -> Void) {
        self.onDismiss = onDismiss
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("faq_title".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 可滾動的主要內容區域
            ZStack {
                ScrollView(.vertical, showsIndicators: false) {
                    VStack(alignment: .leading, spacing: CGFloat(24).auto()) {
                        // 使用共用的 FAQ 內容元件
                        FAQContentView()
                    }
                    .padding(.top, CGFloat(20).auto())
                    .padding(.bottom, CGFloat(20).auto())
                }

                // 頂部淡出漸層
                VStack {
                    LinearGradient(
                        gradient: Gradient(colors: [HexColor.themed(.primaryBackground), HexColor.themed(.primaryBackground).opacity(0)]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .frame(height: CGFloat(30).auto())
                    Spacer()
                }
                .allowsHitTesting(false)
                
                // 底部淡出漸層
                VStack {
                    Spacer()
                    LinearGradient(
                        gradient: Gradient(colors: [HexColor.themed(.primaryBackground).opacity(0), HexColor.themed(.primaryBackground)]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .frame(height: CGFloat(30).auto())
                }
                .allowsHitTesting(false)
            }
            .offset(y: CGFloat(-20).auto())
            .frame(maxHeight: .infinity)

            Spacer()
            
            // 底部按鈕區域
            HStack(spacing: CGFloat(20).auto()) {
                HStack {
                    // 左側文字按鈕
                    Button(action: {
                        onDismiss()
                    }) {
                        Text("close".localized)
                            .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                    }
                    
                    Spacer()
                }
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))
    }
}

// MARK: - 預覽
#Preview {
    FaqView(onDismiss: {})
}
