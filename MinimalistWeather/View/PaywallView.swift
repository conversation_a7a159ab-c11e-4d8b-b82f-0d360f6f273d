//
//  PaywallView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import RevenueCat
import AutoInch

// Paywall 功能展示項目
struct PaywallCarouselItem {
    let titleKey: String
    let subtitleKey: String
    let baseImageName: String

    var title: String {
        return titleKey.localized
    }

    var subtitle: String {
        return subtitleKey.localized
    }

    var imageURL: String {
        let currentLanguage = Locale.current.languageCode ?? "en"
        let region = Locale.current.regionCode ?? ""

        // 判斷是否為繁體中文
        let isTraditionalChinese = (currentLanguage == "zh" && region == "TW") ||
                                  (currentLanguage == "zh" && region == "HK") ||
                                  (currentLanguage == "zh" && region == "MO")

        if isTraditionalChinese {
            return "https://weather.minlsm.com/paywall/zh_tw/\(baseImageName)"
        } else {
            return "https://weather.minlsm.com/paywall/en/\(baseImageName)"
        }
    }

    static let items: [PaywallCarouselItem] = [
        PaywallCarouselItem(
            titleKey: "paywall_home_widget_title",
            subtitleKey: "paywall_home_widget_subtitle",
            baseImageName: "widget.png"
        ),
        PaywallCarouselItem(
            titleKey: "paywall_switch_provider_title",
            subtitleKey: "paywall_switch_provider_subtitle",
            baseImageName: "provider.png"
        ),
        PaywallCarouselItem(
            titleKey: "paywall_night_theme_title",
            subtitleKey: "paywall_night_theme_subtitle",
            baseImageName: "night.png"
        ),
        PaywallCarouselItem(
            titleKey: "paywall_forecasts_120hr_title",
            subtitleKey: "paywall_forecasts_120hr_subtitle",
            baseImageName: "forecast.png"
        ),
        PaywallCarouselItem(
            titleKey: "paywall_saved_50_locations_title",
            subtitleKey: "paywall_saved_50_locations_subtitle",
            baseImageName: "location.png"
        ),
    ]
}

struct PaywallView: View {
    // MARK: - 屬性
    @Environment(\.dismiss) private var dismiss
    @ObservedObject private var iapService = IAPService.shared
    @State private var isYearly = true // 預設選擇年費
    @State private var selectedPackage: Package?
    @State private var monthlyPackage: Package?
    @State private var yearlyPackage: Package?
    @State private var isRestoring = false
    @State private var isPurchasing = false
    @State private var isShowingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    @State private var selectedCarouselIndex = 0
    @State private var isUserInteracting = false // 用戶是否正在互動
    @State private var moveGradient = true

    // Carousel 相關
    let timer = Timer.publish(every: 3.0, on: .main, in: .common).autoconnect()

    // 計算屬性
    private var currentCarouselItem: PaywallCarouselItem {
        PaywallCarouselItem.items[selectedCarouselIndex]
    }

    // 計算年度方案相對於月度方案的折扣百分比
    private var yearlyDiscountText: String {
        guard let yearlyPackage = yearlyPackage,
              let monthlyPackage = monthlyPackage else {
            return "打7折" // 預設值
        }

        let yearlyPrice = yearlyPackage.storeProduct.price.doubleValue
        let monthlyPrice = monthlyPackage.storeProduct.price.doubleValue

        // 計算年度方案的月平均價格
        let yearlyMonthlyPrice = yearlyPrice / 12.0

        // 計算折扣百分比 (相對於月費的節省比例)
        let savingsPercentage = ((monthlyPrice - yearlyMonthlyPrice) / monthlyPrice) * 100

        // 四捨五入到最接近的整數
        let discountPercentage = Int(savingsPercentage.rounded())

        // 中文用「打幾折」表示，例如節省30%就是打7折
        let chineseDiscount = (100 - discountPercentage) / 10
        return "打\(chineseDiscount)折"
    }
    
    // MARK: - 視圖
    var body: some View {
        Group {
            if iapService.isEntitlementActive("pro") {
                proUserView
            } else {
                paywallContentView
            }
        }
        .onAppear {
            iapService.updateCustomerInfo()
            loadOfferings()
        }
        .onChange(of: isYearly) { newValue in
            selectedPackage = newValue ? yearlyPackage : monthlyPackage
        }
        .onReceive(timer) { _ in
            // 只有在用戶沒有互動時才自動切換
            if !isUserInteracting {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    if selectedCarouselIndex < PaywallCarouselItem.items.count - 1 {
                        selectedCarouselIndex += 1
                    } else {
                        selectedCarouselIndex = 0
                    }
                }
            }
        }
        .alert(isPresented: $isShowingAlert) {
            Alert(title: Text(alertTitle.localized), message: Text(alertMessage.localized), dismissButton: .default(Text("ok".localized)))
        }
    }

    // MARK: - Pro 用戶視圖
    private var proUserView: some View {
        let screenWidth = UIScreen.main.bounds.size.width

        return VStack(alignment: .leading, spacing: CGFloat(20).auto()) {

            Rectangle()
                .overlay {
                    LinearGradient(colors: [.clear, .white, .clear], startPoint: .leading, endPoint: .trailing)
                        .frame(width: 50)
                        .offset(x: moveGradient ? -screenWidth/2 : screenWidth/2)
                }
                .animation(.linear(duration: 2).repeatForever(autoreverses: false), value: moveGradient)
                .mask {
                    Text("Premium")
                        .foregroundColor(HexColor.themed(.primaryText))
                        .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .onAppear {
                    self.moveGradient.toggle()
                }
            // .background(.gray)
            .frame(height: CGFloat(20).auto())
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.top, CGFloat(50).auto())
            
            // 標題
            // Text("Premium")
            //     .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
            //     .foregroundColor(HexColor.themed(.primaryText))
            //     .padding(.top, CGFloat(50).auto())
            
            // 可滾動的主要內容區域
            ZStack {
                ScrollView(.vertical, showsIndicators: false) {
                    VStack(alignment: .leading, spacing: CGFloat(0).auto()) {
                        // 副標題
                        Text("thanks_for_pro".localized)
                            .multilineTextAlignment(.leading)
                            .font(.system(size: CGFloat(18).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.mainColor))

                        Text("enjoy_premium_features".localized)
                            .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                            .padding(.top, CGFloat(24).auto())

                        VStack (alignment: .leading, spacing: CGFloat(10).auto()) {
                            HStack {
                                AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(24).auto(), color: HexColor.themed(.accent))
                                    .frame(width: CGFloat(24).auto(), height: CGFloat(24).auto())
                                Text("paywall_switch_provider_title".localized)
                                    .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                            }
                            HStack {
                                AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(24).auto(), color: HexColor.themed(.accent))
                                    .frame(width: CGFloat(24).auto(), height: CGFloat(24).auto())
                                Text("paywall_forecasts_120hr_title".localized)
                                    .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                            }
                            HStack {
                                AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(24).auto(), color: HexColor.themed(.accent))
                                    .frame(width: CGFloat(24).auto(), height: CGFloat(24).auto())
                                Text("paywall_saved_50_locations_title".localized)
                                    .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                            }
                            HStack {
                                AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(24).auto(), color: HexColor.themed(.accent))
                                    .frame(width: CGFloat(24).auto(), height: CGFloat(24).auto())
                                Text("paywall_home_widget_title".localized)
                                    .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                            }
                            HStack {
                                AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(24).auto(), color: HexColor.themed(.accent))
                                    .frame(width: CGFloat(24).auto(), height: CGFloat(24).auto())
                                Text("paywall_night_theme_title".localized)
                                    .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                            }
                            Text("future_premium_enhancements".localized)
                                .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                                .foregroundColor(HexColor.themed(.secondaryText))
                        }
                        .padding(.top, CGFloat(12).auto())

                        VStack(spacing: CGFloat(10).auto()) {
                            AppIconsSymbol.createView(for: AppIcons.procheck, fontSize: CGFloat(128).auto(), color: HexColor.themed(.separator))
                        }
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.top, CGFloat(30).auto())

                        

                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.top, CGFloat(20).auto())
                    .padding(.top, CGFloat(20).auto())
                }

                // 頂部淡出漸層
                VStack {
                    LinearGradient(
                        gradient: Gradient(colors: [HexColor.themed(.primaryBackground), HexColor.themed(.primaryBackground).opacity(0)]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .frame(height: CGFloat(30).auto())
                    Spacer()
                }
                .allowsHitTesting(false)
                
                // 底部淡出漸層
                VStack {
                    Spacer()
                    LinearGradient(
                        gradient: Gradient(colors: [HexColor.themed(.primaryBackground).opacity(0), HexColor.themed(.primaryBackground)]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .frame(height: CGFloat(30).auto())
                }
                .allowsHitTesting(false)
            }
            .offset(y: CGFloat(-20).auto())
            .frame(maxHeight: .infinity)

            Spacer()
            
            // 底部按鈕區域
            HStack(spacing: CGFloat(20).auto()) {
                HStack {
                    // 左側文字按鈕
                    Button(action: {
                        dismiss()
                    }) {
                        Text("close".localized)
                            .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                    }
                    
                    Spacer()
                }
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))

        // ScrollView {
        //     HStack {
        //         // 關閉按鈕
        //         Button(action: {
        //             dismiss()
        //         }) {
        //             AppIconsSymbol.createView(for: AppIcons.close, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
        //         }

        //         Spacer()
        //     }
        //     .padding(.top, CGFloat(40).auto())
        //     .padding(.horizontal, CGFloat(40).auto())

        //     VStack(spacing: CGFloat(8).auto()) {
        //         // 主標題
        //         Text("Premium")
        //             .font(.system(size: CGFloat(36).auto(), weight: .medium, design: .rounded))
        //             .foregroundColor(HexColor.themed(.primaryText))
        //             .padding(.top, CGFloat(4).auto())

        //         AppIconsSymbol.createView(for: AppIcons.procheck, fontSize: CGFloat(100).auto(), color: HexColor.themed(.primaryText))
        //         .padding(.top, CGFloat(30).auto())

        //         // 副標題
        //         Text("thanks_for_pro".localized)
        //             .multilineTextAlignment(.center)
        //             .font(.system(size: CGFloat(18).auto(), weight: .medium, design: .rounded))
        //             .foregroundColor(HexColor.themed(.primaryText))
        //             .frame(width: CGFloat(228).auto(), alignment: .center)

        //         // 描述
        //         Text("pro_description".localized)
        //             .lineLimit(nil)
        //             .lineSpacing(6)
        //             .multilineTextAlignment(.center)
        //             .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
        //             .foregroundColor(HexColor.themed(.secondaryText))
        //             .frame(width: CGFloat(228).auto(), alignment: .center)
        //             .padding(.top, CGFloat(16).auto())
        //             .padding(.bottom, CGFloat(80).auto())

        //         HStack(spacing: CGFloat(10).auto()) {
        //             Text("→")
        //                 .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
        //                 .opacity(0)
        //             Text("feedback".localized)
        //                 .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
        //                 .foregroundColor(HexColor.themed(.secondaryBackground))
        //                 .onTapGesture {
        //                     if let url = URL(string: "https://minlsm.featurebase.app/") {
        //                         UIApplication.shared.open(url)
        //                     }
        //                 }
        //             Text("→")
        //                 .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
        //                 .foregroundColor(HexColor.themed(.secondaryBackground))
        //         }
        //         .padding(.vertical, CGFloat(10).auto())
        //         .frame(width: CGFloat(200).auto())
        //         .background(HexColor.themed(.accent))
        //         .cornerRadius(CGFloat(5).auto())
        //     }
        // }
        // .background(HexColor.themed(.secondaryBackground))
    }

    // MARK: - Paywall 內容視圖
    private var paywallContentView: some View {
        VStack(spacing: 0) {
            // 關閉按鈕
            HStack {
                Spacer()
                Button(action: {
                    dismiss()
                }) {
                    if isPurchasing {
                        AppIconsSymbol.createView(for: AppIcons.close, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
                            .opacity(0)
                    }
                    else {
                        AppIconsSymbol.createView(for: AppIcons.close, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
                    }
                }
            }
            .padding(.top, CGFloat(30).auto())
            .padding(.horizontal, CGFloat(40).auto())

            // Spacer()

            // Carousel 區域
            VStack(spacing: CGFloat(0).auto()) {
                // 動態 Carousel 容器
                GeometryReader { geometry in
                    ZStack {
                        ForEach(Array(PaywallCarouselItem.items.enumerated()), id: \.offset) { index, item in
                            PaywallCarouselCard(
                                item: item,
                                index: index,
                                currentIndex: $selectedCarouselIndex,
                                geometry: geometry
                            )
                            .offset(x: CGFloat(index - selectedCarouselIndex) * geometry.size.width)
                            .opacity(abs(index - selectedCarouselIndex) <= 1 ? 1.0 : 0.0)
                            .scaleEffect(index == selectedCarouselIndex ? 1.0 : 0.9)
                            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: selectedCarouselIndex)
                        }
                    }
                    .gesture(
                        DragGesture()
                            .onChanged { _ in
                                // 手勢開始時暫停自動切換
                                isUserInteracting = true
                            }
                            .onEnded { value in
                                let threshold: CGFloat = geometry.size.width * 0.25

                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    if value.translation.width > threshold {
                                        // 向右滑動 - 上一張
                                        selectedCarouselIndex = max(selectedCarouselIndex - 1, 0)
                                    } else if value.translation.width < -threshold {
                                        // 向左滑動 - 下一張
                                        selectedCarouselIndex = min(selectedCarouselIndex + 1, PaywallCarouselItem.items.count - 1)
                                    }
                                }

                                // 手勢結束後延遲恢復自動切換
                                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                    isUserInteracting = false
                                }
                            }
                    )
                }
                .frame(height: CGFloat(280).auto())
                // .background(HexColor.themed(.success))

                // 標題和副標題
                VStack(spacing: CGFloat(4).auto()) {
                    Text(currentCarouselItem.title)
                        .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.primaryText))
                        .animation(.easeInOut(duration: 0.3), value: selectedCarouselIndex)

                    Text(currentCarouselItem.subtitle)
                        .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(HexColor.themed(.secondaryText))
                        .animation(.easeInOut(duration: 0.3), value: selectedCarouselIndex)
                }
                .padding(.top, CGFloat(4).auto())
                .padding(.bottom, CGFloat(10).auto())

                // 頁面指示器
                HStack(spacing: CGFloat(8).auto()) {
                    ForEach(0..<PaywallCarouselItem.items.count, id: \.self) { index in
                        if index == selectedCarouselIndex {
                            Capsule()
                                .fill(HexColor.themed(.primaryText))
                                .frame(width: CGFloat(16).auto(), height: CGFloat(6).auto())
                                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: selectedCarouselIndex)
                        } else {
                            Circle()
                                .fill(HexColor.themed(.separator))
                                .frame(width: CGFloat(6).auto(), height: CGFloat(6).auto())
                        }
                    }
                }
            }

            Spacer()

            // 價格選擇區域
            VStack(spacing: CGFloat(2).auto()) {
                // 年費選項
                PaywallPricingOption(
                    title: "yearly_plan".localized,
                    price: yearlyPackage?.storeProduct.localizedPriceString ?? "$4.99",
                    discountedPrice: yearlyDiscountText,
                    isSelected: isYearly,
                    onTap: {
                        withAnimation {
                            isYearly = true
                        }
                    }
                )

                Divider()
                    .background(HexColor.themed(.separator))

                // 月費選項
                PaywallPricingOption(
                    title: "monthly_plan".localized,
                    price: monthlyPackage?.storeProduct.localizedPriceString ?? "$0.99",
                    discountedPrice: nil,
                    isSelected: !isYearly,
                    onTap: {
                        withAnimation {
                            isYearly = false
                        }
                    }
                )
            }
            .padding(.horizontal, CGFloat(40).auto())

            // 訂閱按鈕
            Button(action: {
                handlePurchase()
            }) {
                HStack {
                    if isPurchasing {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: HexColor.themed(.secondaryBackground)))
                            .scaleEffect(0.8)
                        Text("purchasing".localized)
                            .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.secondaryBackground))
                    }
                    else {
                        Text("start_7_day_trial".localized)
                            .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.secondaryBackground))
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, CGFloat(12).auto())
                .background(HexColor.themed(.primaryText))
                .cornerRadius(CGFloat(8).auto())
            }
            .disabled(isPurchasing)
            .padding(.horizontal, CGFloat(40).auto())
            .padding(.top, CGFloat(20).auto())

            // Restore 按鈕
            Button(action: {
                restorePurchases()
            }) {
                if isRestoring {
                    HStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: HexColor.color("222222")))
                            .scaleEffect(0.8)
                            
                        Text("restore".localized)
                            .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                        
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: HexColor.color("222222")))
                            .scaleEffect(0.8)
                            .opacity(0)
                    }
                } else {
                    Text("restore".localized)
                        .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.primaryText))
                }
            }
            .disabled(isRestoring)
            .padding(.top, CGFloat(12).auto())

            // Terms & Privacy
            HStack(spacing: CGFloat(4).auto()) {
                Text("terms_of_service".localized)
                    .font(.system(size: CGFloat(12).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.secondaryText))
                    .underline()
                    .onTapGesture {
                        if let url = URL(string: "https://www.minlsm.com/terms") {
                            UIApplication.shared.open(url)
                        }
                    }

                Text("|")
                    .font(.system(size: CGFloat(12).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.secondaryText))

                Text("privacy_policy".localized)
                    .font(.system(size: CGFloat(12).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.secondaryText))
                    .underline()
                    .onTapGesture {
                        if let url = URL(string: "https://www.minlsm.com/privacy") {
                            UIApplication.shared.open(url)
                        }
                    }
            }
            .padding(.top, CGFloat(8).auto())
            .padding(.bottom, CGFloat(40).auto())
        }
        .background(HexColor.themed(.primaryBackground))
    }
    
    // MARK: - 方法

    private func handlePurchase() {
        guard let package = isYearly ? yearlyPackage : monthlyPackage else {
            alertTitle = "error".localized
            alertMessage = "package_not_available".localized
            isShowingAlert = true
            return
        }
        purchasePackage(package)
    }
    
    // 載入商品信息
    private func loadOfferings() {
        iapService.getOfferings { offerings in
            if let current = offerings?.current {
                DispatchQueue.main.async {
                    // 月度套餐
                    self.monthlyPackage = current.monthly
                    
                    // 年度套餐
                    self.yearlyPackage = current.annual
                    
                    // 根據當前選擇設置默認套餐
                    self.selectedPackage = self.isYearly ? self.yearlyPackage : self.monthlyPackage
                }
            }
        }
    }
    
    // 處理購買
    private func purchasePackage(_ package: Package) {
        // 設置正在購買狀態
        isPurchasing = true
        
        iapService.purchasePackage(package) { success, customerInfo, error in
            // 購買完成後，關閉加載狀態
            DispatchQueue.main.async {
                self.isPurchasing = false
                
                if success {
                    // 更新用戶狀態
                    self.iapService.updateCustomerInfo()
                    
                    // 顯示成功訊息
                    self.alertTitle = "subscription_success".localized
                    self.alertMessage = "thank_you_for_subscribing".localized
                    self.isShowingAlert = true
                    Logger.success("購買成功")
                } else if let error = error {
                    // 如果是用戶取消，不顯示錯誤
                    if (error as NSError).domain == "SKErrorDomain" && (error as NSError).code == 2 {
                        Logger.debug("用戶取消購買")
                    } else {
                        // 顯示錯誤訊息
                        self.alertTitle = "purchase_failed".localized
                        self.alertMessage = error.localizedDescription
                        self.isShowingAlert = true
                        Logger.error("購買失敗: \(error.localizedDescription)")
                    }
                }
            }
        }
    }
    
    // 恢復購買
    private func restorePurchases() {
        isRestoring = true

        Purchases.shared.restorePurchases { customerInfo, error in
            DispatchQueue.main.async {
                self.isRestoring = false

                if let error = error {
                    self.alertTitle = "restore_failed".localized
                    self.alertMessage = error.localizedDescription
                    self.isShowingAlert = true
                    Logger.error("恢復購買失敗: \(error.localizedDescription)")
                    return
                }

                guard let customerInfo = customerInfo else {
                    self.alertTitle = "error".localized
                    self.alertMessage = "cannot_get_user_information".localized
                    self.isShowingAlert = true
                    return
                }

                let isPro = customerInfo.entitlements["pro"]?.isActive == true

                if isPro {
                    self.alertTitle = "restore_success".localized
                    self.alertMessage = "purchase_restored".localized
                    Logger.success("恢復購買成功，entitlement 有效")

                    // 自訂回傳處理
                    self.iapService.updateCustomerInfo()

                } else {
                    self.alertTitle = "no_restorable_items".localized
                    self.alertMessage = "no_restorable_items_message".localized
                    Logger.debug("恢復完成，但沒有 entitlement")
                }

                self.isShowingAlert = true
            }
        }
    }
}

// MARK: - Paywall 價格選項視圖
struct PaywallPricingOption: View {
    let title: String
    let price: String
    let discountedPrice: String?
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: CGFloat(0).auto()) {
                // Radio button
                AppIconsSymbol.createView(
                    for: isSelected ? AppIcons.radioboxcheck : AppIcons.radiobox,
                    fontSize: CGFloat(32).auto(),
                    color: isSelected ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                )
                .opacity(isSelected ? 1.0 : 0.3)

                VStack(alignment: .leading, spacing: CGFloat(2).auto()) {
                    Text(title)
                        .font(.system(size: CGFloat(14).auto(), weight: .semibold, design: .rounded))
                        .foregroundColor(isSelected ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                        .opacity(isSelected ? 1.0 : 0.3)

                    if let discountedPrice = discountedPrice {
                        Text(discountedPrice)
                            .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.accent))
                            .opacity(isSelected ? 1.0 : 0.3)
                    }
                }

                Spacer()

                Text(price)
                    .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(isSelected ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                    .opacity(isSelected ? 1.0 : 0.3)
            }
            // .offset(x: CGFloat(-10).auto())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Paywall Carousel Card 視圖
struct PaywallCarouselCard: View {
    let item: PaywallCarouselItem
    let index: Int
    @Binding var currentIndex: Int
    let geometry: GeometryProxy

    var body: some View {
        VStack(spacing: CGFloat(0).auto()) {
            // 圖片內容
            AsyncImage(url: URL(string: item.imageURL)) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: CGFloat(320).auto(), height: CGFloat(280).auto())
                    .clipped()
            } placeholder: {
                RoundedRectangle(cornerRadius: CGFloat(0).auto())
                    .fill(HexColor.themed(.primaryBackground))
                    .frame(width: CGFloat(320).auto(), height: CGFloat(280).auto())
                    .overlay(
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: HexColor.themed(.primaryText)))
                    )
            }
        }
        .frame(width: geometry.size.width, height: CGFloat(280).auto())
    }
}

// MARK: - 預覽
#Preview {
    PaywallView()
}
