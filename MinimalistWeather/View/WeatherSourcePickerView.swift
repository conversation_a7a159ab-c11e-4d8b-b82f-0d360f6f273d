//
//  WeatherSourcePickerView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

// MARK: - 天氣來源選擇器模態框
struct WeatherSourcePickerView: View {
    @Binding var selectedSource: WeatherSource
    @Environment(\.presentationMode) var presentationMode
    let availableSources: [WeatherSource]
    let onDismiss: () -> Void
    let onConfirm: () -> Void

    // 內部狀態來確保 UI 更新
    @State private var internalSelectedSource: WeatherSource

    // IAP 相關
    @ObservedObject private var iapService = IAPService.shared
    @State private var showingUpgradeAlert = false
    @State private var showingPaywall = false

    // 天氣來源詳細資訊
    @State private var showingWeatherSourceInfo = false
    
    init(selectedSource: Binding<WeatherSource>, availableSources: [WeatherSource], onDismiss: @escaping () -> Void, onConfirm: @escaping () -> Void) {
        self._selectedSource = selectedSource
        self.availableSources = availableSources
        self.onDismiss = onDismiss
        self.onConfirm = onConfirm
        self._internalSelectedSource = State(initialValue: selectedSource.wrappedValue)
    }

    // MARK: - 計算屬性

    /// 檢查是否可以選擇天氣來源（需要 Pro 訂閱）
    private var canSelectWeatherSource: Bool {
        return iapService.isPro
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("weather_source_setting".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 主要內容區域
            VStack(alignment: .leading, spacing: CGFloat(8).auto()) {

                // 檢查是否有可用來源
                if availableSources.isEmpty {
                    VStack(alignment: .leading, spacing: CGFloat(12).auto()) {
                        Text("no_available_weather_sources".localized)
                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))

                        Text("請聯繫開發者以解決此問題")
                            .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                            .foregroundColor(HexColor.themed(.secondaryText))
                    }
                    .padding(.vertical, CGFloat(20).auto())
                } else {
                    // 動態顯示可用的天氣來源
                    ForEach(availableSources, id: \.self) { source in
                    Button(action: {
                        if canSelectWeatherSource {
                            // 有 Pro 訂閱，可以選擇天氣來源
                            withAnimation(.easeInOut(duration: 0.2)) {
                                internalSelectedSource = source
                                // 不立即更新 selectedSource，只更新內部狀態
                            }
                            Logger.debug("選擇天氣來源: \(source.displayName)")
                        } else {
                            // 沒有 Pro 訂閱，顯示升級提示
                            showUpgradeAlert()
                        }
                    }) {
                        HStack(spacing: CGFloat(0).auto()) {
                            // Radio button
                            AppIconsSymbol.createView(
                                for: internalSelectedSource == source ? AppIcons.radioboxcheck : AppIcons.radiobox,
                                fontSize: CGFloat(44).auto(),
                                color: internalSelectedSource == source ? HexColor.themed(.mainColor) : HexColor.themed(.secondaryText)
                            )
                            .opacity(canSelectWeatherSource ? (internalSelectedSource == source ? 1.0 : 0.3) : 0.2)
                            .animation(.easeInOut(duration: 0.2), value: internalSelectedSource)

                            Text(source.displayName)
                                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(internalSelectedSource == source ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                                .opacity(canSelectWeatherSource ? (internalSelectedSource == source ? 1.0 : 0.3) : 0.4)
                                .animation(.easeInOut(duration: 0.2), value: internalSelectedSource)
                        }
                        .offset(x: CGFloat(-10).auto())
                    }
                }
                }
            }
            .padding(.top, CGFloat(12).auto())

            Divider()
            
            Button(action: {
                showingWeatherSourceInfo = true
            }) {
                VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
                    Text("more_providers_info".localized)
                        .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.primaryText))
                        .padding(.bottom, CGFloat(2).auto())
                    Text("different_weather_models_info".localized)
                        .font(.system(size: CGFloat(10).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(HexColor.themed(.secondaryText))
                }
                .padding(.top, CGFloat(0).auto())
            }
            .buttonStyle(PlainButtonStyle())
            
            Spacer()
            
            // 底部按鈕區域
            HStack(spacing: CGFloat(20).auto()) {
                HStack {
                    // 左側文字按鈕
                    Button(action: {
                        onDismiss()
                    }) {
                        Text("cancel".localized)
                            .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                    }
                    
                    Spacer()
                    
                    // 右側圓形勾選按鈕
                    Button(action: {
                        if canSelectWeatherSource {
                            // 確認時才更新 selectedSource
                            selectedSource = internalSelectedSource
                            onConfirm()
                        } else {
                            // 沒有 Pro 訂閱，顯示升級提示
                            showUpgradeAlert()
                        }
                    }) {
                        HStack(spacing: CGFloat(8).auto()) {
                            AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(44).auto(), color: HexColor.themed(.secondaryBackground))
                        }
                        .padding(.horizontal, CGFloat(2).auto())
                        .padding(.vertical, CGFloat(2).auto())
                        .background(
                            Circle()
                                .fill(HexColor.themed(.button))
                        )
                    }
                }
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(HexColor.themed(.primaryBackground))
        .iapUpgradeAlert(isPresented: $showingUpgradeAlert, showingPaywall: $showingPaywall)
        .fullScreenCover(isPresented: $showingPaywall) {
            PaywallView()
        }
        .fullScreenCover(isPresented: $showingWeatherSourceInfo) {
            WeatherSourceInfoView(onDismiss: {
                showingWeatherSourceInfo = false
            })
        }
        .onAppear {
            Logger.debug("🎯 === WeatherSourcePickerView onAppear ===")
            Logger.debug("🎯 availableSources 數量: \(availableSources.count)")
            Logger.debug("🎯 availableSources: \(availableSources.map { $0.displayName })")
            Logger.debug("🎯 selectedSource: \(selectedSource.displayName)")
            Logger.debug("🎯 internalSelectedSource: \(internalSelectedSource.displayName)")
            Logger.debug("🎯 canSelectWeatherSource: \(canSelectWeatherSource)")

            // 檢查視圖是否正確初始化
            if availableSources.isEmpty {
                Logger.debug("🎯 ❌ 警告：availableSources 為空！")
            } else {
                Logger.debug("🎯 ✅ availableSources 正常")
            }
            Logger.debug("🎯 ====================================")

            // 強制刷新內部狀態
            DispatchQueue.main.async {
                internalSelectedSource = selectedSource
                Logger.debug("🎯 內部狀態已刷新")
            }
        }
        .onChange(of: iapService.isPro) { isPro in
            // 當 IAP 狀態變化時，同步 UI 狀態
            if !isPro {
                // 降級到免費版，重置為預設天氣來源
                internalSelectedSource = selectedSource
                Logger.debug("🔧 UI 狀態已同步：重置天氣來源選擇")
            }
        }
    }

    // MARK: - 私有方法

    /// 顯示升級提示
    private func showUpgradeAlert() {
        showingUpgradeAlert = true
    }
}

// MARK: - 預覽
#Preview {
    WeatherSourcePickerView(
        selectedSource: .constant(.OW),
        availableSources: [.AW, .OW, .GW],
        onDismiss: {},
        onConfirm: {}
    )
} 