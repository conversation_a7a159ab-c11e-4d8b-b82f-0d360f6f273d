//
//  ThemeColorSwatchPreview.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/8/3.
//

import SwiftUI
import AutoInch

/// 主題顏色色票預覽
struct ThemeColorSwatchPreview: View {
    
    var body: some View {
        ScrollView {
            VStack(spacing: CGFloat(24).auto()) {
                Text("主題顏色色票預覽")
                    .font(.system(size: CGFloat(24).auto(), weight: .bold, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryText))
                    .padding(.top, CGFloat(20).auto())
                
                // 所有主題的顏色色票
                VStack(spacing: CGFloat(16).auto()) {
                    ForEach(ThemeMode.allCases, id: \.self) { theme in
                        ThemeColorSwatchRow(theme: theme)
                    }
                }
                .padding(.horizontal, CGFloat(20).auto())
                
                Spacer(minLength: CGFloat(40).auto())
            }
        }
        .background(HexColor.themed(.primaryBackground))
    }
}

/// 單個主題的顏色色票行
struct ThemeColorSwatchRow: View {
    let theme: ThemeMode
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(12).auto()) {
            // 主題名稱
            Text(theme.displayName)
                .font(.system(size: CGFloat(18).auto(), weight: .semibold, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
            
            // 顏色色票網格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: CGFloat(8).auto()), count: 4), spacing: CGFloat(8).auto()) {
                ColorSwatchItem(title: "背景", color: getThemeColor(.primaryBackground, for: theme))
                ColorSwatchItem(title: "主色", color: getThemeColor(.mainColor, for: theme))
                ColorSwatchItem(title: "文字", color: getThemeColor(.primaryText, for: theme))
                ColorSwatchItem(title: "次要", color: getThemeColor(.secondaryText, for: theme))
            }
            
            // 大色票預覽
            HStack(spacing: CGFloat(0).auto()) {
                Rectangle()
                    .fill(getThemeColor(.primaryBackground, for: theme))
                    .frame(height: CGFloat(40).auto())
                
                Rectangle()
                    .fill(getThemeColor(.mainColor, for: theme))
                    .frame(height: CGFloat(40).auto())
            }
            .cornerRadius(CGFloat(8).auto())
            .overlay(
                RoundedRectangle(cornerRadius: CGFloat(8).auto())
                    .stroke(HexColor.themed(.separator), lineWidth: CGFloat(1).auto())
            )
        }
        .padding(CGFloat(16).auto())
        .background(
            RoundedRectangle(cornerRadius: CGFloat(12).auto())
                .fill(HexColor.themed(.secondaryBackground))
        )
    }
    
    /// 獲取指定主題的顏色
    private func getThemeColor(_ colorTheme: HexColor.ColorTheme, for theme: ThemeMode) -> Color {
        let colorMap = getThemeColorMap(for: theme)
        guard let hexValue = colorMap[colorTheme] else {
            return Color.gray
        }
        return HexColor.color(hexValue)
    }
    
    /// 獲取主題顏色映射
    private func getThemeColorMap(for theme: ThemeMode) -> [HexColor.ColorTheme: String] {
        switch theme {
        case .light:
            return [
                .primaryBackground: "F9F9F9",
                .mainColor: "222222",
                .primaryText: "222222",
                .secondaryText: "888888"
            ]
        case .dark:
            return [
                .primaryBackground: "222222",
                .mainColor: "F9F9F9",
                .primaryText: "F9F9F9",
                .secondaryText: "AEAEB2"
            ]
        case .muji:
            return [
                .primaryBackground: "FFFFFF",
                .mainColor: "7F0019",
                .primaryText: "3C3C3C",
                .secondaryText: "8B7355"
            ]
        case .ana:
            return [
                .primaryBackground: "FFFFFF",
                .mainColor: "001F3F",
                .primaryText: "001F3F",
                .secondaryText: "888888"
            ]
        }
    }
}

/// 單個顏色色票項目
struct ColorSwatchItem: View {
    let title: String
    let color: Color
    
    var body: some View {
        VStack(spacing: CGFloat(4).auto()) {
            Rectangle()
                .fill(color)
                .frame(height: CGFloat(24).auto())
                .cornerRadius(CGFloat(4).auto())
                .overlay(
                    RoundedRectangle(cornerRadius: CGFloat(4).auto())
                        .stroke(Color.gray.opacity(0.3), lineWidth: CGFloat(0.5).auto())
                )
            
            Text(title)
                .font(.system(size: CGFloat(10).auto(), weight: .medium, design: .rounded))
                .foregroundColor(HexColor.themed(.secondaryText))
        }
    }
}

// MARK: - 預覽
#Preview {
    ThemeColorSwatchPreview()
}
