//
//  ThemePreviewView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/8/3.
//

import SwiftUI
import AutoInch

/// 主題預覽視圖（用於測試新主題）
struct ThemePreviewView: View {
    @State private var selectedTheme: ThemeMode = .light
    
    var body: some View {
        VStack(spacing: CGFloat(20).auto()) {
            // 主題選擇器
            Picker("選擇主題", selection: $selectedTheme) {
                ForEach(ThemeMode.allCases, id: \.self) { theme in
                    Text(theme.displayName).tag(theme)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding()
            
            // 主題預覽區域
            VStack(spacing: CGFloat(16).auto()) {
                Text("主題預覽")
                    .font(.system(size: CGFloat(24).auto(), weight: .bold, design: .rounded))
                    .foregroundColor(getThemeColor(.primaryText, for: selectedTheme))
                
                Text("這是主要文字顏色")
                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(getThemeColor(.primaryText, for: selectedTheme))
                
                Text("這是次要文字顏色")
                    .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(getThemeColor(.secondaryText, for: selectedTheme))
                
                Text("這是輔助文字顏色")
                    .font(.system(size: CGFloat(12).auto(), weight: .light, design: .rounded))
                    .foregroundColor(getThemeColor(.tertiaryText, for: selectedTheme))
                
                // 按鈕示例
                Button("示例按鈕") {
                    // 空動作
                }
                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                .foregroundColor(getThemeColor(.secondaryBackground, for: selectedTheme))
                .padding(.horizontal, CGFloat(20).auto())
                .padding(.vertical, CGFloat(10).auto())
                .background(
                    RoundedRectangle(cornerRadius: CGFloat(8).auto())
                        .fill(getThemeColor(.accent, for: selectedTheme))
                )
                
                // 分隔線
                Rectangle()
                    .fill(getThemeColor(.separator, for: selectedTheme))
                    .frame(height: CGFloat(1).auto())
                    .padding(.horizontal, CGFloat(20).auto())
                
                // 卡片示例
                VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
                    Text("卡片標題")
                        .font(.system(size: CGFloat(18).auto(), weight: .semibold, design: .rounded))
                        .foregroundColor(getThemeColor(.primaryText, for: selectedTheme))
                    
                    Text("這是卡片內容，展示次要背景色的效果。")
                        .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(getThemeColor(.secondaryText, for: selectedTheme))
                }
                .padding(CGFloat(16).auto())
                .background(
                    RoundedRectangle(cornerRadius: CGFloat(12).auto())
                        .fill(getThemeColor(.secondaryBackground, for: selectedTheme))
                )
                .padding(.horizontal, CGFloat(20).auto())
            }
            .padding(CGFloat(20).auto())
            .background(
                RoundedRectangle(cornerRadius: CGFloat(16).auto())
                    .fill(getThemeColor(.primaryBackground, for: selectedTheme))
            )
            .padding()
            
            Spacer()
        }
        .background(Color.gray.opacity(0.1))
    }
    
    /// 獲取指定主題的顏色
    private func getThemeColor(_ colorTheme: HexColor.ColorTheme, for theme: ThemeMode) -> Color {
        guard let colorMap = getThemeColorMap(for: theme),
              let hexValue = colorMap[colorTheme] else {
            return Color.primary
        }
        return HexColor.color(hexValue)
    }
    
    /// 獲取主題顏色映射
    private func getThemeColorMap(for theme: ThemeMode) -> [HexColor.ColorTheme: String]? {
        switch theme {
        case .light:
            return [
                .primaryBackground: "F9F9F9",
                .secondaryBackground: "FFFFFF",
                .primaryText: "222222",
                .secondaryText: "888888",
                .tertiaryText: "CCCCCC",
                .accent: "222222",
                .separator: "DEDEDE"
            ]
        case .dark:
            return [
                .primaryBackground: "222222",
                .secondaryBackground: "2C2C2E",
                .primaryText: "F9F9F9",
                .secondaryText: "AEAEB2",
                .tertiaryText: "636366",
                .accent: "F9F9F9",
                .separator: "38383A"
            ]
        case .muji:
            return [
                .primaryBackground: "F5F5DC",
                .secondaryBackground: "FEFEFE",
                .primaryText: "3C3C3C",
                .secondaryText: "8B7355",
                .tertiaryText: "D2B48C",
                .accent: "8B4513",
                .separator: "E6E6E6"
            ]
        case .ana:
            return [
                .primaryBackground: "001F3F",
                .secondaryBackground: "003366",
                .primaryText: "FFFFFF",
                .secondaryText: "87CEEB",
                .tertiaryText: "4682B4",
                .accent: "00BFFF",
                .separator: "2F4F4F"
            ]
        // case .system:
        //     // 系統主題使用淺色主題作為預覽
        //     return getThemeColorMap(for: .light)
        }
    }
}

// MARK: - 預覽
#Preview {
    ThemePreviewView()
}
