//
//  WeatherSourceInfoView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/7/19.
//

import SwiftUI
import AutoInch

// MARK: - 天氣來源詳細資訊視圖
struct WeatherSourceInfoView: View {
    @Environment(\.presentationMode) var presentationMode
    let onDismiss: () -> Void

    init(onDismiss: @escaping () -> Void) {
        self.onDismiss = onDismiss
    }

    // 定義每個資料來源的連結
    private let sourceLinks: [String: String] = [
        "weather_source_jma": "https://www.jma.go.jp/jma/indexe.html",
        "weather_source_eccc": "https://weather.gc.ca/",
        "weather_source_dwd": "https://www.dwd.de/DE/Home/home_node.html",
        "weather_source_nws_noaa": "https://www.weather.gov/",
        "weather_source_metoffice_ecmwf": "https://www.metoffice.gov.uk/",
        "weather_source_weather_com": "https://weather.com/",
        "weather_source_cwb": "https://www.cwa.gov.tw/",
        "weather_source_environment_canada": "https://weather.gc.ca/",
        "weather_source_eumetnet": "https://www.eumetnet.eu/",
        "weather_source_ecmwf": "https://www.ecmwf.int/",
        "weather_source_noaa": "https://www.noaa.gov/",
        "weather_source_metoffice": "https://www.metoffice.gov.uk/",
        "weather_source_gem_cmc": "https://weather.gc.ca/grib/index_e.html"
    ]
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("weather_providers_info".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 可滾動的主要內容區域
            ZStack {
                ScrollView(.vertical, showsIndicators: false) {
                    VStack(alignment: .leading, spacing: CGFloat(24).auto()) {
                        
                        // OpenWeather 區塊
                        WeatherProviderSection(
                            title: "weather_provider_openweather".localized,
                            subtitle: "weather_provider_openweather_subtitle".localized,
                            sources: [
                                "weather_source_metoffice",
                                "weather_source_ecmwf",
                                "weather_source_noaa",
                                "weather_source_gem_cmc"
                            ],
                            sourceLinks: sourceLinks
                        )
                        
                        Divider()
                            .background(HexColor.themed(.secondaryText).opacity(0.3))
                        
                        // Central Weather Administration 區塊
                        WeatherProviderSection(
                            title: "weather_provider_cwa".localized,
                            subtitle: "weather_provider_cwa_subtitle".localized,
                            sources: [
                                "weather_source_cwb"
                            ],
                            sourceLinks: sourceLinks
                        )
                        
                        Divider()
                            .background(HexColor.themed(.secondaryText).opacity(0.3))
                        
                        // Google Weather 區塊
                        WeatherProviderSection(
                            title: "weather_provider_google".localized,
                            subtitle: "weather_provider_google_subtitle".localized,
                            sources: [
                                "weather_source_dwd",
                                "weather_source_environment_canada",
                                "weather_source_eumetnet",
                                "weather_source_ecmwf",
                                "weather_source_noaa",
                                "weather_source_nws_noaa",
                                "weather_source_metoffice"  
                            ],
                            sourceLinks: sourceLinks
                        )

                        Divider()
                            .background(HexColor.themed(.secondaryText).opacity(0.3))

                        // Apple Weather 區塊
                        WeatherProviderSection(
                            title: "weather_provider_apple".localized,
                            subtitle: "weather_provider_apple_subtitle".localized,
                            sources: [
                                "weather_source_jma",
                                "weather_source_eccc",
                                "weather_source_dwd",
                                "weather_source_nws_noaa",
                                "weather_source_metoffice_ecmwf",
                            ],
                            sourceLinks: sourceLinks
                        )                        
                    }
                    .padding(.top, CGFloat(20).auto())
                    .padding(.bottom, CGFloat(20).auto())
                }

                // 頂部淡出漸層
                VStack {
                    LinearGradient(
                        gradient: Gradient(colors: [HexColor.themed(.primaryBackground), HexColor.themed(.primaryBackground).opacity(0)]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .frame(height: CGFloat(30).auto())
                    Spacer()
                }
                .allowsHitTesting(false)
                
                // 底部淡出漸層
                VStack {
                    Spacer()
                    LinearGradient(
                        gradient: Gradient(colors: [HexColor.themed(.primaryBackground).opacity(0), HexColor.themed(.primaryBackground)]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .frame(height: CGFloat(30).auto())
                }
                .allowsHitTesting(false)
            }
            .frame(maxHeight: .infinity)

            Spacer()
            
            // 底部按鈕區域
            HStack(spacing: CGFloat(20).auto()) {
                HStack {
                    // 左側文字按鈕
                    Button(action: {
                        onDismiss()
                    }) {
                        Text("close".localized)
                            .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                    }
                    
                    Spacer()
                }
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))
    }
}

// MARK: - 天氣提供商區塊視圖
struct WeatherProviderSection: View {
    let title: String
    let subtitle: String
    let sources: [String]
    let sourceLinks: [String: String]

    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(12).auto()) {
            // 標題
            Text(title)
                .font(.system(size: CGFloat(18).auto(), weight: .semibold, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
            
            // 副標題
            Text(subtitle)
                .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                .foregroundColor(HexColor.themed(.secondaryText))
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
                // .opacity(0.0)
                // .frame(height: 0)
            
            // 來源列表
            VStack(alignment: .leading, spacing: CGFloat(12).auto()) {
                ForEach(sources, id: \.self) { source in
                    Button(action: {
                        if let url = sourceLinks[source], let urlObject = URL(string: url) {
                            UIApplication.shared.open(urlObject)
                            Logger.debug("開啟連結: \(url)")
                        } else {
                            Logger.warning("找不到連結: \(source)")
                        }
                    }) {
                        HStack(alignment: .top, spacing: CGFloat(8).auto()) {
                            // Text("•")
                            //     .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                            //     .foregroundColor(HexColor.themed(.primaryText))

                            Text(source.localized)
                                .font(.system(size: CGFloat(11).auto(), weight: .regular, design: .rounded))
                                .foregroundColor(HexColor.themed(.secondaryText))
                                .underline()
                                .lineLimit(nil)
                                .fixedSize(horizontal: false, vertical: true)

                            Spacer()
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.leading, CGFloat(0).auto())
        }
    }
}

// MARK: - 預覽
#Preview {
    WeatherSourceInfoView(onDismiss: {})
}
