//
//  TimelinePreviewScrollView.swift
//  MinimalistWeather
//
//  Created by Ke<PERSON> on 2025/5/15.
//

import SwiftUI
import AutoInch

/// 時間軸預覽滾動視圖，顯示五天每三小時的天氣預報
struct TimelinePreviewScrollView: View {
    // MARK: - 屬性
    var timelineData: [TimelineHourlyData]
    @Binding var selectedIndex: Int
    @Binding var isVisible: Bool
    var timezone: String? // 當前選擇地區的時區
    var onUserInteraction: (() -> Void)? = nil // 新增：用戶互動回調
    var onWeatherDetailOpen: (() -> Void)? = nil // 新增：開啟天氣詳細視圖回調
    
    // 新增：固定顯示狀態
    @State private var isPinned: Bool = false
    
    // ScrollView的捲動位置
    @State private var scrollOffset: CGFloat = 0
    @State private var scrollViewWidth: CGFloat = 0

    // 控制溫度和降雨機率交替顯示
    @State private var showingTemperature: Bool = true
    private let displayToggleTimer = Timer.publish(every: 2, on: .main, in: .common).autoconnect()
    
    // 設定每個項目的寬度，確保與時間軸刻度間距一致
    private let itemWidth: CGFloat = 90 // 確保項目有足夠的空間顯示
    
    // 計算選中項目的視圖位置
    private var selectedItemOffset: CGFloat {
        return CGFloat(selectedIndex) * itemWidth
    }
    
    // 計算滾動比例
    private var scrollRatio: CGFloat {
        guard !timelineData.isEmpty else { return 0 }
        let contentWidth = CGFloat(timelineData.count) * itemWidth
        return contentWidth > 0 ? scrollOffset / contentWidth : 0
    }

    // MARK: - 初始化器
    init(timelineData: [TimelineHourlyData], selectedIndex: Binding<Int>, isVisible: Binding<Bool>, timezone: String? = nil, onUserInteraction: (() -> Void)? = nil, onWeatherDetailOpen: (() -> Void)? = nil) {
        self.timelineData = timelineData
        self._selectedIndex = selectedIndex
        self._isVisible = isVisible
        self.timezone = timezone
        self.onUserInteraction = onUserInteraction
        self.onWeatherDetailOpen = onWeatherDetailOpen
    }

   
    
    // MARK: - 主視圖
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .topTrailing) {
                ScrollViewReader { scrollProxy in
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: CGFloat(12).auto()) {
                            // 添加前面的空白填充，確保第一個項目能居中顯示
                            Spacer()
                                .frame(width: geometry.size.width / 2 - itemWidth / 2) // 確保第一個項目正好在中心
                            
                            ForEach(0..<timelineData.count, id: \.self) { index in
                                hourlyItemView(for: index)
                                    .frame(width: itemWidth)
                                    .id(index)
                            }
                            
                            // 添加後面的空白填充
                            Spacer()
                                .frame(width: geometry.size.width / 2 - itemWidth / 2)
                        }
                        .background(GeometryReader { geo in
                            Color.clear.preference(
                                key: ScrollOffsetPreferenceKey.self,
                                value: -geo.frame(in: .named("scrollView")).origin.x
                            )
                        })
                    }
                    .coordinateSpace(name: "scrollView")
                    .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                        scrollOffset = value
                    }
                    .gesture(
                        // 添加手勢來檢測用戶滾動互動
                        DragGesture(minimumDistance: 5)
                            .onChanged { _ in
                                onUserInteraction?() // 觸發用戶互動回調
                            }
                    )
                    .onAppear {
                        scrollViewWidth = geometry.size.width
                        
                        // 初始滾動到選中的項目
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            if isVisible {
                                withAnimation(.easeOut(duration: 0.3)) {
                                    scrollToSelected(scrollProxy)
                                }
                            }
                        }
                    }
                    .onChange(of: selectedIndex) { newValue in
                        if isVisible {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                scrollToSelected(scrollProxy)
                            }
                        }
                    }
                    .onChange(of: isVisible) { newValue in
                        if newValue {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                withAnimation(.easeOut(duration: 0.3)) {
                                    scrollToSelected(scrollProxy)
                                }
                            }
                        }
                    }
                }
                // .background(Color.red)
                // 添加手勢響應區域，用於長按固定預報視圖
                .contentShape(Rectangle())
                // .onLongPressGesture(minimumDuration: 0.5) {
                //     withAnimation(.spring(response: 0.3)) {
                //         isPinned.toggle()
                //     }
                //     // 如果取消固定，通知外部視圖可以隱藏
                //     if !isPinned {
                //         isVisible = true
                //     }
                // }
            }
        }
        .transition(.opacity)
        .onChange(of: isPinned) { newValue in
            // 更新外部isVisible狀態
            if newValue {
                isVisible = true
            }
        }
        .onReceive(displayToggleTimer) { _ in
            // 只有在視圖可見時才切換顯示
            if isVisible {
                withAnimation(.easeInOut(duration: 0.5)) {
                    showingTemperature.toggle()
                }
            }
        }
        .onDisappear {
            // 清理計時器，避免內存洩漏
            displayToggleTimer.upstream.connect().cancel()
        }
    }
    
    // MARK: - 輔助方法
    
    /// 滾動到選中的項目
    private func scrollToSelected(_ proxy: ScrollViewProxy) {
        proxy.scrollTo(selectedIndex, anchor: .center)
    }
    
    /// 每小時項目視圖
    private func hourlyItemView(for index: Int) -> some View {
        let isSelected = index == selectedIndex
        let item = timelineData[index]
        
        return VStack(spacing: CGFloat(0).auto()) {

            // 時間（根據系統設定顯示）
            Group {
                let displayTime = getFormattedTimeForDisplay(item: item, index: index)
                let timeComponents = parseFormattedTime(displayTime)
                
                HStack(spacing: CGFloat(1).auto()) {
                    Text(timeComponents.time)
                        .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.primaryText))
                    if !timeComponents.ampm.isEmpty {
                        Text(timeComponents.ampm)
                            .font(.system(size: CGFloat(10).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                    }
                }
            }
            .padding(.top, CGFloat(30).auto())
            .padding(.bottom, CGFloat(12).auto())
            .offset(y: isSelected ? CGFloat(-4).auto() : CGFloat(0).auto())
            // .padding(.bottom, (isSelected ? CGFloat(12).auto() : CGFloat(4).auto()))
            .lineLimit(1)
            .minimumScaleFactor(0.8)
            .frame(width: min(itemWidth - CGFloat(10).auto(), CGFloat(80).auto()), alignment: .center)
            .scaleEffect(isSelected ? 1.3 : 1.0)
            .animation(.spring(response: 0.3), value: isSelected)
            .opacity(isSelected ? 1.0 : 0.5)
            
            // 天氣圖標
            AppIconsSymbol.createView(
                for: AppIconsSymbol.getWeatherIconFromCode(item.iconCode),
                fontSize: CGFloat(70).auto(),
                // color: isSelected ? HexColor.color("222222") : HexColor.color("555555")
                color: HexColor.themed(.primaryText)
            )
            .scaleEffect(isSelected ? 1.3 : 1.0)
            .animation(.spring(response: 0.3), value: isSelected)
            .opacity(isSelected ? 1.0 : 0.5)
            .onTapGesture {
                // 只有選中的項目才能開啟天氣詳細視圖
                if isSelected {
                    onWeatherDetailOpen?()
                }
            }
            
            // 降雨機率長條圖
            let rainProb = item.precipitationProbability.replacingOccurrences(of: "%", with: "")
            let rainProbValue = Int(rainProb) ?? 0
            
            VStack(spacing: CGFloat(2).auto()) {
                Spacer() // 使VStack靠底部對齊
                    
                // 長條圖
                GeometryReader { geo in
                    let maxHeight: CGFloat = 80.auto() // 調整最大高度
                    // 確保即使是小概率也有最小高度顯示
                    let minBarHeight: CGFloat = 5.auto()
                    let calculatedHeight = CGFloat(rainProbValue) / 100.0 * maxHeight
                    let barHeight = max(minBarHeight, calculatedHeight)
                    
                    // 長條
                    RoundedRectangle(cornerRadius: CGFloat(4).auto())
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    HexColor.themed(.rainpBartop),
                                    HexColor.themed(.separator)
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .frame(width: CGFloat(8).auto(), height: barHeight)
                        .position(x: geo.size.width / 2, y: geo.size.height - barHeight / 2)
                }
                .frame(height: CGFloat(80).auto())
                // 降雨機率數值標籤
                HStack(spacing: CGFloat(1).auto()) {
                    Text("%")
                        .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.primaryBackground))
                    Text(rainProb)
                        .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(isSelected ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                    Text("%")
                        .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(isSelected ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                }
                .padding(.top, CGFloat(4).auto())
                .scaleEffect(isSelected ? 1.2 : 1.0)
                .animation(.spring(response: 0.3), value: isSelected)
                .opacity(isSelected ? 1.0 : 0.5)
            }
            .padding(.bottom, CGFloat(16).auto())
        }
        .frame(width: itemWidth) // 確保整個項目寬度固定
        .scaleEffect(isSelected ? 1.0 : 1.0)
        .animation(.spring(response: 0.1), value: isSelected)
        .onTapGesture {
            withAnimation(.spring(response: 0.1)) {
                selectedIndex = index
            }
            onUserInteraction?() // 觸發用戶互動回調
        }
    }

    /// 獲取格式化的時間顯示（基於 RulerTestView 的正確邏輯）
    private func getFormattedTimeForDisplay(item: TimelineHourlyData, index: Int) -> String {
        // 第一個項目顯示 "NOW"
        if index == 0 {
            return "now".localized
        }
        
        // 檢查時間格式
        if item.time.contains(" ") {
            // API 格式時間（如 "2023-10-25 12:00:00"），使用共用方法處理
            return DateTimeFormatter.shared.formatAPITimeForTimeline(item.time, timezoneId: timezone)
        } else if item.time.contains(":") && item.time.count <= 5 {
            // 簡單時間格式（如 "14:30"），直接格式化
            return DateTimeFormatter.shared.formatTimeString(item.time)
        } else {
            // 已經格式化的時間字串，直接返回
            return item.time
        }
    }
    
    /// 解析格式化後的時間字串
    private func parseFormattedTime(_ formattedTime: String) -> (time: String, ampm: String) {
        // 檢查是否包含 AM/PM
        let ampmPattern = "\\b(AM|PM|上午|下午|午前|午後|am|pm)\\b"
        let regex = try? NSRegularExpression(pattern: ampmPattern, options: .caseInsensitive)
        let range = NSRange(location: 0, length: formattedTime.utf16.count)
        
        if let match = regex?.firstMatch(in: formattedTime, options: [], range: range) {
            let ampmRange = Range(match.range, in: formattedTime)!
            let ampm = String(formattedTime[ampmRange])
            let time = formattedTime.replacingOccurrences(of: ampm, with: "").trimmingCharacters(in: .whitespaces)
            return (time: time, ampm: ampm)
        } else {
            return (time: formattedTime, ampm: "")
        }
    }
}

// 用於追蹤ScrollView滾動位置的PreferenceKey
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - 預覽
struct TimelinePreviewScrollView_Previews: PreviewProvider {
    static var previews: some View {
        TimelinePreviewScrollView(
            timelineData: [
                TimelineHourlyData(time: "10:00", date: Date(), temperature: "25°C", iconCode: "01d", precipitationProbability: "0%", humidity: "45%", lowHighTemp: "23 ~ 27"),
                TimelineHourlyData(time: "13:00", date: Date().addingTimeInterval(3600 * 3), temperature: "28°C", iconCode: "02d", precipitationProbability: "20%", humidity: "48%", lowHighTemp: "25 ~ 30"),
                TimelineHourlyData(time: "16:00", date: Date().addingTimeInterval(3600 * 6), temperature: "26°C", iconCode: "03d", precipitationProbability: "10%", humidity: "50%", lowHighTemp: "24 ~ 29"),
                TimelineHourlyData(time: "19:00", date: Date().addingTimeInterval(3600 * 9), temperature: "22°C", iconCode: "04d", precipitationProbability: "5%", humidity: "52%", lowHighTemp: "20 ~ 25")
            ],
            selectedIndex: .constant(1),
            isVisible: .constant(true),
            timezone: nil,
            onUserInteraction: nil,
            onWeatherDetailOpen: nil
        )
        .previewLayout(.fixed(width: 375, height: 200))
    }
} 