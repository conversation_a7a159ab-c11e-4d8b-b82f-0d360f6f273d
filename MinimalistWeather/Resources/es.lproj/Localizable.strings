/* 
  Localizable.strings (Spanish)
  MinimalistWeather
*/

// MARK: - Onboarding
"welcome" = "Bienvenido";
"onboarding_title_before_begin" = "Antes de empezar";
"terms_privacy" = "Términos y Privacidad";
"privacy_policy" = "Política de privacidad";
"terms_of_service" = "Términos de servicio";
"accept" = "¡Entendido!";
"location" = "Ubicaciones";
"add_forecasts_city" = "Añadir ubicación";
"searching" = "Buscando...";
"confirm_add" = "Confirmar";
"add_more_cities" = "Añadir más ubicaciones";
"choose_your_plan" = "Elige tu plan";
"feature_plans_available" = "Los planes de funciones estarán disponibles aquí";
"coming_soon" = "Próximamente...";
"continue" = "Continuar";
"all_set" = "¡Todo listo!";
"ready_to_start" = "Ya puedes empezar a usar Minimalist Weather";
"get_started" = "Empezar";

// MARK: - Main App
"no_connection" = "SIN CONEXIÓN";
"check_internet_connection" = "Por favor, comprueba tu conexión a internet";
"feedback" = "Opiniones";

// MARK: - FAQ
"faq_title" = "Preguntas Frecuentes";
"loading_faq" = "Cargando FAQ...";
"error_loading_faq" = "Error al cargar las FAQ";
"retry" = "Reintentar";

// MARK: - Weather
"server_error" = "Tiempo de conexión agotado";
"send_feedback_check_announcements" = "Intenta recargar, cambiar de proveedor o comprobar el estado.";
"status_check" = "Comprobar estado";

// MARK: - Settings
"unit" = "UNIDAD";
"pro" = "Ajustes Pro";
"language" = "Idioma";
"time_format" = "Formato de hora";
"12_hour_format" = "Formato de 12 horas";
"24_hour_format" = "Formato de 24 horas";

// MARK: - Location Search
"search_city_name" = "Introduce una ubicación o dirección";
"no_locations_matched" = "NO SE ENCONTRARON UBICACIONES";
"search_for_a_city" = "BUSCAR UNA CIUDAD";
"no_saved_locations" = "NO HAY UBICACIONES GUARDADAS";
"unlimited_locations_available" = "Ubicaciones ilimitadas disponibles.";
"upgrade_to_pro_add_more" = "Actualiza a Pro para añadir más ubicaciones.";

// MARK: - Paywall
"thanks_for_pro" = "¡Gracias por pasarte a Premium! Nos encantaría oír tus ideas.";
"enjoy_premium_features" = "Ahora tienes acceso completo a:";
"future_premium_enhancements" = "Y esto es solo el principio: ¡más funciones Premium están en camino!";
"pro_description" = "Si tienes preguntas o sugerencias, no dudes en decírnoslo.";
"unlock_full_experience" = "Desbloquea la experiencia minimalista completa";
"5_day_forecast" = "Pronóstico de 5 días";
"multiple_locations" = "Múltiples ubicaciones";
"all_future_features" = "Todas las futuras funciones incluidas";
"purchasing" = "Comprando...";
"upgrade" = "Actualizar";
"restoring" = "Restaurando...";
"restore" = "Restaurar";
"purchase_agreement" = "Términos | Privacidad";
"terms" = "Términos";
"loading" = "CARGANDO";

// MARK: - Common
"ok" = "OK";
"cancel" = "Cancelar";
"search" = "Buscar";
"close" = "Cerrar";
"add" = "Añadir";
"delete" = "Eliminar";
"edit" = "Editar";
"retry" = "Recargar";
"auto_retrying" = "Recargando...";
"change_weather_source" = "Cambiar proveedor";

// MARK: - Time
"am" = "a. m.";
"pm" = "p. m.";
"now" = "AHORA";

// MARK: - Weekdays
"monday" = "LUN";
"tuesday" = "MAR";
"wednesday" = "MIÉ";
"thursday" = "JUE";
"friday" = "VIE";
"saturday" = "SÁB";
"sunday" = "DOM";

// MARK: - Weather Units
"celsius" = "°C";
"fahrenheit" = "°F";
"percent" = "%";

// MARK: - Feature Plans
"current_weather_forecast" = "Pronóstico del tiempo actual";
"3_hour_2_day_forecast" = "Pronóstico de 48 horas";
"1_location_forecast" = "Pronóstico de 1 ubicación";
"3_hour_5_day_forecast" = "Pronóstico del tiempo de 5 días";
"2_location_forecast" = "Pronóstico de 50 ciudades";
"detailed_weather_info" = "Datos meteorológicos detallados";
"custom_night_theme" = "Tema nocturno personalizado";
"no_ads" = "Sin anuncios";
"start_free" = "Empezar gratis";
"start_7_day_trial" = "Prueba gratis de 3 días";
"monthly_plan" = "Mensual";
"yearly_plan" = "Anual -50 %";

// MARK: - Alerts
"no_connection_alert" = "Sin conexión";
"connect_internet_message" = "Conéctate a internet para las actualizaciones del tiempo.";

// MARK: - IAP Upgrade
"upgrade_to_pro" = "Actualizar a Pro";
"upgrade_now" = "Actualizar ahora";
"upgrade_message_multiple_locations" = "Actualiza a Pro para añadir múltiples ubicaciones y más funciones.";

// MARK: - Setup Completion Alert
"please_complete_setup" = "Por favor, completa la configuración";
"complete_setup_before_using" = "Debes completar la configuración antes de usar esta función.";
"go_to_setup" = "Ir a configuración";

// MARK: - Subscription Period
"per_year" = "/año";
"per_month" = "/mes";

// MARK: - Settings Menu
"about" = "ACERCA DE";

// MARK: - Sunrise Sunset
"sunrise" = "Amanecer";
"sunset" = "Atardecer";
"sunset_sunrise" = "Amanecer y atardecer";

// MARK: - Paywall Continue
"continue_using" = "Seguir usando";

// MARK: - Google Geocoding Errors
"network_error" = "Error de conexión de red";
"invalid_response" = "Respuesta no válida";
"api_error" = "Prueba con una ubicación diferente o más específica";
"decoding_error" = "No se pudieron leer los datos";
"no_search_results" = "No se encontraron resultados";

// MARK: - Settings Page Titles
"temperature_unit_setting" = "Unidad de temperatura";
"time_format_setting" = "Formato de hora";
"theme_setting" = "Ajuste del tema";
"weather_source_setting" = "Proveedor del tiempo";

// MARK: - Theme Settings
"theme_system" = "Sistema";
"theme_light" = "Tema claro";
"theme_dark" = "Tema oscuro";

// MARK: - Temperature Units with Symbols
"celsius_with_symbol" = "Celsius °C";
"fahrenheit_with_symbol" = "Fahrenheit °F";

// MARK: - Weather Sources
"apple_weather" = "Apple Weather";
"google_weather" = "Google Weather";
"central_weather_administration" = "Central Weather Administration";

// MARK: - Widget Settings
"widget_settings" = "Ajustes del widget";

// MARK: - What's New
"whats_new" = "Novedades";
"show_whats_new" = "Mostrar novedades";
"release_notes" = "Notas de la versión";
"version" = "Versión";
"no_updates_available" = "No hay actualizaciones disponibles";

// MARK: - Purchase & Subscription Alerts
"purchase_failed" = "Error en la compra";
"subscription_success" = "Suscripción correcta";
"thank_you_for_subscribing" = "¡Gracias por suscribirte!";
"error" = "Error";
"package_not_available" = "Paquete no disponible";
"cannot_get_user_information" = "No se puede obtener la información del usuario";
"restore_failed" = "Error al restaurar";
"restore_success" = "Restauración correcta";
"purchase_restored" = "Tu compra ha sido restaurada";
"no_restorable_items" = "No hay elementos que restaurar";
"no_restorable_items_message" = "No pudimos encontrar ninguna compra anterior que restaurar.";

// MARK: - Paywall Carousel
"paywall_forecasts_120hr_title" = "Desbloquea el pronóstico completo";
"paywall_forecasts_120hr_subtitle" = "Pronósticos a largo plazo y datos detallados.";
"paywall_saved_50_locations_title" = "50 ubicaciones guardadas";
"paywall_saved_50_locations_subtitle" = "La versión gratuita está limitada a 1 ubicación.";
"paywall_home_widget_title" = "Widget de inicio";
"paywall_home_widget_subtitle" = "No disponible en la versión gratuita.";
"paywall_night_theme_title" = "Tema oscuro";
"paywall_night_theme_subtitle" = "El tema oscuro es una función premium.";
"paywall_switch_provider_title" = "Cambiar de proveedor";
"paywall_switch_provider_subtitle" = "No disponible en la versión gratuita.";

// Unknown weather
"weather_unknown" = "Tiempo desconocido";

// MARK: - Measurement System
"metric_system" = "Métrico";
"imperial_system" = "Imperial";
"measurement_system_setting" = "Sistema de medida";

// MARK: - Wind Speed Units
"wind_speed_ms" = "m/s";
"wind_speed_mph" = "mph";

// MARK: - Distance Units
"distance_km" = "km";
"distance_mi" = "mi";

// MARK: - Precipitation Units
"precipitation_mm" = "mm";
"precipitation_in" = "in";

// MARK: - Pressure Units
"pressure_hpa" = "hPa";
"pressure_inhg" = "inHg";

// MARK: - Weather Detail Labels
"feels_like" = "Sensación";
"humidity" = "Humedad";
"precipitation_probability" = "Precipitación";
"cloudiness" = "Nubosidad";
"uv_index" = "Índice UV";
"daily_max_uv_index" = "Índice UV Máximo Diario";
"wind_speed" = "Viento";
"wind_gust" = "Ráfagas";
"visibility" = "Visibilidad";
"sea_pressure" = "Presión marina";
"ground_pressure" = "Presión terrestre";
"rain_volume" = "Lluvia";
"snow_volume" = "Nieve";

// MARK: - Weather Providers
"weather_providers_info" = "Info de proveedor";
"more_providers_info" = "Más info de proveedor →";
"different_weather_models_info" = "Diferentes modelos meteorológicos ofrecen distintos tiempos de pronóstico.";
"weather_provider_apple" = "El Tiempo de Apple";
"weather_provider_apple_subtitle" = "Compatible con todos los países\nPronóstico de 120 horas";
"weather_provider_cwa" = "Central Weather Administration";
"weather_provider_cwa_subtitle" = "Solo compatible con Taiwán\nPronóstico de 72 horas";
"weather_provider_google" = "El Tiempo de Google";
"weather_provider_google_subtitle" = "No compatible con Japón o Corea\nPronóstico de 120 horas";
"weather_provider_openweather" = "OpenWeather";
"weather_provider_openweather_subtitle" = "Compatible con todos los países\nPronóstico de 120 horas";

// MARK: - Weather Sources
"weather_source_jma" = "Japan Meteorological Agency";
"weather_source_eccc" = "Environment and Climate Change Canada";
"weather_source_dwd" = "Deutscher Wetterdienst";
"weather_source_nws_noaa" = "National Weather Service";
"weather_source_metoffice_ecmwf" = "The Met Office/European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_weather_com" = "Weather";
"weather_source_cwb" = "Weather Forecast Center";
"weather_source_environment_canada" = "Environment Canada";
"weather_source_eumetnet" = "EUMETNET";
"weather_source_ecmwf" = "European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_noaa" = "National Oceanic and Atmospheric Administration (NOAA)";
"weather_source_metoffice" = "Met Office";
"weather_source_gem_cmc" = "GEM (CMC, Canadian Meteorological Centre)";

// MARK: - Weather Data Status
"maintenance_status" = "En mantenimiento";
