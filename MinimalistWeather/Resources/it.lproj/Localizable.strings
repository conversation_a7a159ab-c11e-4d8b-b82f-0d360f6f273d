/* 
  Localizable.strings (Italian)
  MinimalistWeather
*/

// MARK: - Onboarding
"welcome" = "Benvenuto";
"onboarding_title_before_begin" = "Prima di iniziare";
"terms_privacy" = "Termini e Privacy";
"privacy_policy" = "Informativa sulla privacy";
"terms_of_service" = "Termini di servizio";
"accept" = "Ho capito";
"location" = "Località";
"add_forecasts_city" = "Aggiungi una località";
"searching" = "Ricerca...";
"confirm_add" = "Conferma";
"add_more_cities" = "Aggiungi altre località";
"choose_your_plan" = "Scegli il tuo piano";
"feature_plans_available" = "I piani saranno disponibili qui";
"coming_soon" = "In arrivo...";
"continue" = "Continua";
"all_set" = "Tutto pronto!";
"ready_to_start" = "Sei pronto per usare Minimalist Weather";
"get_started" = "Inizia";

// MARK: - Main App
"no_connection" = "NESSUNA CONNESSIONE";
"check_internet_connection" = "Controlla la tua connessione internet";
"feedback" = "Feedback";

// MARK: - FAQ
"faq_title" = "Domande Frequenti";
"loading_faq" = "Caricamento FAQ...";
"error_loading_faq" = "Errore nel caricamento delle FAQ";
"retry" = "Riprova";

// MARK: - Weather
"server_error" = "Timeout della connessione";
"send_feedback_check_announcements" = "Prova a ricaricare, a cambiare fornitore o a controllare lo stato.";
"status_check" = "Controlla stato";

// MARK: - Settings
"unit" = "UNITÀ";
"pro" = "Impostazioni Pro";
"language" = "Lingua";
"time_format" = "Formato ora";
"12_hour_format" = "Formato 12 ore";
"24_hour_format" = "Formato 24 ore";

// MARK: - Location Search
"search_city_name" = "Inserisci località o indirizzo";
"no_locations_matched" = "NESSUNA LOCALITÀ TROVATA";
"search_for_a_city" = "CERCA UNA CITTÀ";
"no_saved_locations" = "NESSUNA LOCALITÀ SALVATA";
"unlimited_locations_available" = "Località illimitate disponibili.";
"upgrade_to_pro_add_more" = "Passa a Pro per aggiungere altre località.";

// MARK: - Paywall
"thanks_for_pro" = "Grazie per l'upgrade a Premium! Le tue idee sono preziose.";
"enjoy_premium_features" = "Ora hai accesso completo a:";
"future_premium_enhancements" = "E siamo solo all'inizio: altre funzioni Premium sono in arrivo!";
"pro_description" = "Se hai domande o suggerimenti, non esitare a farcelo sapere.";
"unlock_full_experience" = "Sblocca l'esperienza minimal completa";
"5_day_forecast" = "Previsioni a 5 giorni";
"multiple_locations" = "Più località";
"all_future_features" = "Tutte le funzioni future incluse";
"purchasing" = "Acquisto in corso...";
"upgrade" = "Aggiorna";
"restoring" = "Ripristino...";
"restore" = "Ripristina";
"purchase_agreement" = "Termini | Privacy";
"terms" = "Termini";
"loading" = "CARICAMENTO";

// MARK: - Common
"ok" = "OK";
"cancel" = "Annulla";
"search" = "Cerca";
"close" = "Chiudi";
"add" = "Aggiungi";
"delete" = "Elimina";
"edit" = "Modifica";
"retry" = "Ricarica";
"auto_retrying" = "Ricaricamento...";
"change_weather_source" = "Cambia fornitore";

// MARK: - Time
"am" = "AM";
"pm" = "PM";
"now" = "ORA";

// MARK: - Weekdays
"monday" = "LUN";
"tuesday" = "MAR";
"wednesday" = "MER";
"thursday" = "GIO";
"friday" = "VEN";
"saturday" = "SAB";
"sunday" = "DOM";

// MARK: - Weather Units
"celsius" = "°C";
"fahrenheit" = "°F";
"percent" = "%";

// MARK: - Feature Plans
"current_weather_forecast" = "Previsioni meteo attuali";
"3_hour_2_day_forecast" = "Previsioni a 48 ore";
"1_location_forecast" = "Previsioni per 1 località";
"3_hour_5_day_forecast" = "Previsioni meteo a 5 giorni";
"2_location_forecast" = "Previsioni per 50 città";
"detailed_weather_info" = "Dati meteo dettagliati";
"custom_night_theme" = "Tema notte personalizzato";
"no_ads" = "Senza pubblicità";
"start_free" = "Inizia gratis";
"start_7_day_trial" = "Prova gratuita di 3 giorni";
"monthly_plan" = "Mensile";
"yearly_plan" = "Annuale -50%";

// MARK: - Alerts
"no_connection_alert" = "Nessuna connessione";
"connect_internet_message" = "Connettiti a internet per gli aggiornamenti meteo.";

// MARK: - IAP Upgrade
"upgrade_to_pro" = "Passa a Pro";
"upgrade_now" = "Aggiorna ora";
"upgrade_message_multiple_locations" = "Passa a Pro per aggiungere più località e funzioni.";

// MARK: - Setup Completion Alert
"please_complete_setup" = "Completa la configurazione";
"complete_setup_before_using" = "Devi completare la configurazione prima di usare questa funzione.";
"go_to_setup" = "Vai a Impostazioni";

// MARK: - Subscription Period
"per_year" = "/anno";
"per_month" = "/mese";

// MARK: - Settings Menu
"about" = "INFORMAZIONI";

// MARK: - Sunrise Sunset
"sunrise" = "Alba";
"sunset" = "Tramonto";
"sunset_sunrise" = "Alba e tramonto";

// MARK: - Paywall Continue
"continue_using" = "Continua a usare";

// MARK: - Google Geocoding Errors
"network_error" = "Errore di connessione";
"invalid_response" = "Risposta non valida";
"api_error" = "Prova una località diversa o più specifica";
"decoding_error" = "Impossibile leggere i dati";
"no_search_results" = "Nessun risultato";

// MARK: - Settings Page Titles
"temperature_unit_setting" = "Unità di temperatura";
"time_format_setting" = "Formato ora";
"theme_setting" = "Impostazione tema";
"weather_source_setting" = "Fornitore meteo";

// MARK: - Theme Settings
"theme_system" = "Sistema";
"theme_light" = "Tema chiaro";
"theme_dark" = "Tema scuro";

// MARK: - Temperature Units with Symbols
"celsius_with_symbol" = "Celsius °C";
"fahrenheit_with_symbol" = "Fahrenheit °F";

// MARK: - Weather Sources
"apple_weather" = "Apple Weather";
"google_weather" = "Google Weather";
"central_weather_administration" = "Central Weather Administration";

// MARK: - Widget Settings
"widget_settings" = "Impostazioni widget";

// MARK: - What's New
"whats_new" = "Novità";
"show_whats_new" = "Mostra novità";
"release_notes" = "Note di rilascio";
"version" = "Versione";
"no_updates_available" = "Nessun aggiornamento";

// MARK: - Purchase & Subscription Alerts
"purchase_failed" = "Acquisto non riuscito";
"subscription_success" = "Abbonamento riuscito";
"thank_you_for_subscribing" = "Grazie per esserti abbonato!";
"error" = "Errore";
"package_not_available" = "Pacchetto non disponibile";
"cannot_get_user_information" = "Impossibile ottenere le info utente";
"restore_failed" = "Ripristino non riuscito";
"restore_success" = "Ripristino riuscito";
"purchase_restored" = "Il tuo acquisto è stato ripristinato";
"no_restorable_items" = "Nessun elemento ripristinabile";
"no_restorable_items_message" = "Non abbiamo trovato acquisti precedenti da ripristinare.";

// MARK: - Paywall Carousel
"paywall_forecasts_120hr_title" = "Sblocca le previsioni complete";
"paywall_forecasts_120hr_subtitle" = "Previsioni a lungo raggio e dati dettagliati.";
"paywall_saved_50_locations_title" = "50 località salvate";
"paywall_saved_50_locations_subtitle" = "La versione gratuita è limitata a 1 località.";
"paywall_home_widget_title" = "Widget Home";
"paywall_home_widget_subtitle" = "Non disponibile nella versione gratuita.";
"paywall_night_theme_title" = "Tema scuro";
"paywall_night_theme_subtitle" = "Il tema scuro è una funzione premium.";
"paywall_switch_provider_title" = "Cambia fornitore";
"paywall_switch_provider_subtitle" = "Non disponibile nella versione gratuita.";

// Unknown weather
"weather_unknown" = "Meteo sconosciuto";

// MARK: - Measurement System
"metric_system" = "Metrico";
"imperial_system" = "Imperiale";
"measurement_system_setting" = "Sistema di misura";

// MARK: - Wind Speed Units
"wind_speed_ms" = "m/s";
"wind_speed_mph" = "mph";

// MARK: - Distance Units
"distance_km" = "km";
"distance_mi" = "mi";

// MARK: - Precipitation Units
"precipitation_mm" = "mm";
"precipitation_in" = "in";

// MARK: - Pressure Units
"pressure_hpa" = "hPa";
"pressure_inhg" = "inHg";

// MARK: - Weather Detail Labels
"feels_like" = "Percepita";
"humidity" = "Umidità";
"precipitation_probability" = "Precipitazioni";
"cloudiness" = "Nuvolosità";
"uv_index" = "Indice UV";
"daily_max_uv_index" = "Indice UV Massimo Giornaliero";
"wind_speed" = "Vento";
"wind_gust" = "Raffiche";
"visibility" = "Visibilità";
"sea_pressure" = "Pressione mare";
"ground_pressure" = "Pressione suolo";
"rain_volume" = "Pioggia";
"snow_volume" = "Neve";

// MARK: - Weather Providers
"weather_providers_info" = "Info fornitore";
"more_providers_info" = "Più info fornitore →";
"different_weather_models_info" = "Modelli meteo diversi offrono tempi di previsione diversi.";
"weather_provider_apple" = "Meteo di Apple";
"weather_provider_apple_subtitle" = "Supporta tutti i paesi\nPrevisioni a 120 ore";
"weather_provider_cwa" = "Central Weather Administration";
"weather_provider_cwa_subtitle" = "Solo per Taiwan\nPrevisioni a 72 ore";
"weather_provider_google" = "Meteo di Google";
"weather_provider_google_subtitle" = "Non supporta Giappone o Corea\nPrevisioni a 120 ore";
"weather_provider_openweather" = "OpenWeather";
"weather_provider_openweather_subtitle" = "Supporta tutti i paesi\nPrevisioni a 120 ore";

// MARK: - Weather Sources
"weather_source_jma" = "Japan Meteorological Agency";
"weather_source_eccc" = "Environment and Climate Change Canada";
"weather_source_dwd" = "Deutscher Wetterdienst";
"weather_source_nws_noaa" = "National Weather Service";
"weather_source_metoffice_ecmwf" = "The Met Office/European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_weather_com" = "Weather";
"weather_source_cwb" = "Weather Forecast Center";
"weather_source_environment_canada" = "Environment Canada";
"weather_source_eumetnet" = "EUMETNET";
"weather_source_ecmwf" = "European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_noaa" = "National Oceanic and Atmospheric Administration (NOAA)";
"weather_source_metoffice" = "Met Office";
"weather_source_gem_cmc" = "GEM (CMC, Canadian Meteorological Centre)";

// MARK: - Weather Data Status
"maintenance_status" = "In manutenzione";
