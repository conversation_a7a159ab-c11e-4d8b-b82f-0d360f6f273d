import SwiftUI

struct HexColor {
    /// 將十六進制顏色代碼轉換為Color
    /// - Parameter hex: 十六進制顏色代碼，格式可為: "F9F9F9" 或 "#F9F9F9"
    /// - Returns: 對應的SwiftUI Color
    static func color(_ hex: String) -> Color {
        let (r, g, b, a) = hexToRGBA(hex)
        return Color(red: Double(r) / 255.0, green: Double(g) / 255.0, blue: Double(b) / 255.0, opacity: Double(a) / 255.0)
    }
    
    /// 將十六進制顏色代碼轉換為RGBA元組
    /// - Parameter hex: 十六進制顏色代碼，格式可為: "F9F9F9" 或 "#F9F9F9"
    /// - Returns: 包含紅、綠、藍、透明度的元組 (r, g, b, a)，每個值範圍為0-255
    static func hexToRGBA(_ hex: String) -> (r: Int, g: Int, b: Int, a: Int) {
        var hexString = hex.trimmingCharacters(in: .whitespacesAndNewlines).uppercased()
        
        // 移除可能存在的#前綴
        if hexString.hasPrefix("#") {
            hexString.remove(at: hexString.startIndex)
        }
        
        // 默認透明度為255（完全不透明）
        var alpha = 255
        var rgb = 0
        
        // 檢查是否有透明度值
        if hexString.count == 8 {
            // 格式為 RRGGBBAA
            if let intValue = Int(hexString, radix: 16) {
                alpha = intValue & 0xFF
                rgb = intValue >> 8
            }
        } else if hexString.count == 6 {
            // 格式為 RRGGBB
            if let intValue = Int(hexString, radix: 16) {
                rgb = intValue
            }
        } else {
            // 無效的十六進制格式
            Logger.debug("無效的十六進制顏色格式: \(hex)")
            return (0, 0, 0, 255)
        }
        
        // 分離RGB值
        let red = (rgb >> 16) & 0xFF
        let green = (rgb >> 8) & 0xFF
        let blue = rgb & 0xFF
        
        return (red, green, blue, alpha)
    }
    
    /// 將十六進制顏色代碼轉換為UIColor
    /// - Parameter hex: 十六進制顏色代碼，格式可為: "F9F9F9" 或 "#F9F9F9"
    /// - Returns: 對應的UIColor
    static func uiColor(_ hex: String) -> UIColor {
        let (r, g, b, a) = hexToRGBA(hex)
        return UIColor(red: CGFloat(r) / 255.0, green: CGFloat(g) / 255.0, blue: CGFloat(b) / 255.0, alpha: CGFloat(a) / 255.0)
    }
}

// MARK: - 主題管理
extension HexColor {
    /// 顏色主題枚舉
    enum ColorTheme {
        /// 主要背景色
        case primaryBackground
        /// 次要背景色
        case secondaryBackground
        /// 主要文字顏色
        case primaryText
        /// 次要文字顏色
        case secondaryText
        /// 輔助文字顏色
        case tertiaryText
        /// 按鈕顏色
        case button
        /// 圖標按鈕顏色
        case mainColor
        /// 降雨條顏色
        case rainpBartop
        /// 強調顏色
        case accent
        /// 分隔線顏色
        case separator
        /// 禁用狀態顏色
        case progressBar
        case disabled
        /// 漸層起始顏色
        case gradientStart
        /// 漸層結束顏色
        case gradientEnd
        /// 圖表顏色
        case chart
        /// 成功狀態顏色
        case success
        /// 陰影顏色
        case shadow
        /// Premium
        case premium
        /// UV 指數顏色 1 (0-2)
        case uvOne
        /// UV 指數顏色 2 (3-5)
        case uvTwo
        /// UV 指數顏色 3 (6-7)
        case uvThree
        /// UV 指數顏色 4 (8-11)
        case uvFour
    }
    
    /// 主題顏色配置
    private static let themeColors: [ThemeMode: [ColorTheme: String]] = [
        .light: [
            .primaryBackground: "F9F9F9",
            .secondaryBackground: "FFFFFF",
            .primaryText: "222222",
            .secondaryText: "888888",
            .tertiaryText: "CCCCCC",
            .button: "222222",
            .mainColor: "222222",
            .rainpBartop: "CCCCCC",
            .accent: "222222",
            .separator: "DEDEDE",
            .progressBar: "EEEEEE",
            .disabled: "CCCCCC",
            .gradientStart: "888888",
            .gradientEnd: "B0B0B0",
            .chart: "AAAAAA",
            .success: "009966",
            .shadow: "888888",
            .premium: "B60000",
            .uvOne: "EEEEEE",
            .uvTwo: "FFE99A",
            .uvThree: "FFD586",
            .uvFour: "FFAAAA"
        ],
        .dark: [
            .primaryBackground: "222222",
            .secondaryBackground: "2C2C2E",
            .primaryText: "F9F9F9",
            .secondaryText: "AEAEB2",
            .tertiaryText: "636366",
            .button: "F9F9F9",
            .mainColor: "F9F9F9",
            .rainpBartop: "636366",
            .accent: "F9F9F9",
            .separator: "38383A",
            .progressBar: "38383A",
            .disabled: "636366",
            .gradientStart: "AEAEB2",
            .gradientEnd: "636366",
            .chart: "8E8E93",
            .success: "30D158",
            .shadow: "000000",
            .premium: "B60000",
            .uvOne: "EEEEEE",
            .uvTwo: "FFE99A",
            .uvThree: "FFD586",
            .uvFour: "FFAAAA"
        ],
        .muji: [
            .primaryBackground: "FFFFFF",
            .secondaryBackground: "FEFEFE",
            .primaryText: "3C3C3C",
            .secondaryText: "8B7355",
            .tertiaryText: "D2B48C",
            .button: "7F0019",
            .mainColor: "7F0019",
            .rainpBartop: "D2B48C",
            .accent: "8B4513",
            .separator: "E6E6E6",
            .progressBar: "FAF7EB",
            .disabled: "D2B48C",
            .gradientStart: "8B7355",
            .gradientEnd: "D2B48C",
            .chart: "A0522D",
            .success: "228B22",
            .shadow: "8B7355",
            .premium: "B60000",
            .uvOne: "F0F0F0",
            .uvTwo: "FFE99A",
            .uvThree: "FFD586",
            .uvFour: "FFAAAA"
        ],
        .ana: [
            .primaryBackground: "FFFFFF",
            .secondaryBackground: "D4EBFB",
            .primaryText: "001F3F",
            .secondaryText: "888888",
            .tertiaryText: "CCCCCC",
            .button: "001F3F",
            .mainColor: "001F3F",
            .rainpBartop: "D4EBFB",
            .accent: "001F3F",
            .separator: "DEDEDE",
            .progressBar: "D4EBFB",
            .disabled: "CCCCCC",
            .gradientStart: "87CEEB",
            .gradientEnd: "4682B4",
            .chart: "AAAAAA",
            .success: "009966",
            .shadow: "888888",
            .premium: "D4EBFB",
            .uvOne: "EEEEEE",
            .uvTwo: "FFE99A",
            .uvThree: "FFD586",
            .uvFour: "FFAAAA"
        ]
        // .ana: [
        //     .primaryBackground: "001F3F",
        //     .secondaryBackground: "003366",
        //     .primaryText: "FFFFFF",
        //     .secondaryText: "87CEEB",
        //     .tertiaryText: "4682B4",
        //     .button: "FFFFFF",
        //     .mainColor: "FFFFFF",
        //     .rainpBartop: "4682B4",
        //     .accent: "00BFFF",
        //     .separator: "2F4F4F",
        //     .progressBar: "2F4F4F",
        //     .disabled: "4682B4",
        //     .gradientStart: "87CEEB",
        //     .gradientEnd: "4682B4",
        //     .chart: "5F9EA0",
        //     .success: "00CED1",
        //     .shadow: "000080",
        //     .premium: "B60000",
        //     .uvOne: "F0F0F0",
        //     .uvTwo: "FFE99A",
        //     .uvThree: "FFD586",
        //     .uvFour: "FFAAAA"
        // ]
    ]
    
    /// 獲取當前主題下的顏色
    /// - Parameter colorTheme: 顏色主題
    /// - Returns: 對應的 SwiftUI Color
    static func themed(_ colorTheme: ColorTheme) -> Color {
        let currentTheme = getCurrentTheme()

        guard let colorMap = themeColors[currentTheme],
              let hexValue = colorMap[colorTheme] else {
            Logger.warning("找不到顏色主題 \(colorTheme) 在 \(currentTheme) 的配置")
            return Color.primary
        }

        return color(hexValue)
    }

    /// 獲取當前主題下的 UIColor
    /// - Parameter colorTheme: 顏色主題
    /// - Returns: 對應的 UIColor
    static func themedUIColor(_ colorTheme: ColorTheme) -> UIColor {
        let currentTheme = getCurrentTheme()

        guard let colorMap = themeColors[currentTheme],
              let hexValue = colorMap[colorTheme] else {
            Logger.warning("找不到顏色主題 \(colorTheme) 在 \(currentTheme) 的配置")
            return UIColor.label
        }

        return uiColor(hexValue)
    }
    
    /// 獲取當前主題模式
    /// - Returns: 當前的主題模式
    private static func getCurrentTheme() -> ThemeMode {
        let settings = AppSettings.shared
        let themeMode = settings.themeMode

        switch themeMode {
        // case .system:
        //     // 根據系統外觀判斷
        //     if #available(iOS 13.0, *) {
        //         return UITraitCollection.current.userInterfaceStyle == .dark ? .dark : .light
        //     } else {
        //         return .light
        //     }
        case .light, .dark, .muji, .ana:
            return themeMode
        }
    }
}

// MARK: - 向後兼容的便利方法
extension HexColor {
    /// 主要背景色 (向後兼容)
    static var primaryBackground: Color { themed(.primaryBackground) }
    /// 次要背景色 (向後兼容)
    static var secondaryBackground: Color { themed(.secondaryBackground) }
    /// 主要文字顏色 (向後兼容)
    static var primaryText: Color { themed(.primaryText) }
    /// 次要文字顏色 (向後兼容)
    static var secondaryText: Color { themed(.secondaryText) }
    /// 輔助文字顏色 (向後兼容)
    static var tertiaryText: Color { themed(.tertiaryText) }
    /// 強調顏色 (向後兼容)
    static var accent: Color { themed(.accent) }
    /// 分隔線顏色 (向後兼容)
    static var separator: Color { themed(.separator) }
    /// 禁用狀態顏色 (向後兼容)
    static var disabled: Color { themed(.disabled) }
    /// 成功狀態顏色 (向後兼容)
    static var success: Color { themed(.success) }
    /// Premium 顏色 (向後兼容)
    static var premium: Color { themed(.premium) }
}
