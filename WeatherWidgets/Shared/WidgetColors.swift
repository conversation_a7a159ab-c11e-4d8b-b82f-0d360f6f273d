//
//  WidgetColors.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import SwiftUI

// MARK: - Widget 顏色管理

/// Widget 專用的顏色管理
struct WidgetColors {
    /// 主題模式
    enum ThemeMode: String {
        case light = "light"
        case dark = "dark"
        case system = "system"
        case muji = "muji"
        case ana = "ana"
    }

    /// 顏色主題
    enum ColorTheme {
        case primaryBackground
        case primaryText
        case secondaryText
    }

    /// 主題顏色配置
    private static let themeColors: [ThemeMode: [ColorTheme: Color]] = [
        .light: [
            .primaryBackground: Color(red: 0.976, green: 0.976, blue: 0.976), // F9F9F9
            .primaryText: Color(red: 0.133, green: 0.133, blue: 0.133),       // 222222
            .secondaryText: Color(red: 0.533, green: 0.533, blue: 0.533)      // 888888
        ],
        .dark: [
            .primaryBackground: Color(red: 0.133, green: 0.133, blue: 0.133), // 222222
            .primaryText: Color(red: 0.976, green: 0.976, blue: 0.976),       // F9F9F9
            .secondaryText: Color(red: 0.682, green: 0.682, blue: 0.714)      // AEAEB2
        ],
        .muji: [
            .primaryBackground: Color(red: 1.0, green: 1.0, blue: 1.0), // F5F5DC
            .primaryText: Color(red: 0.235, green: 0.235, blue: 0.235),       // 3C3C3C
            .secondaryText: Color(red: 0.545, green: 0.451, blue: 0.333)      // 8B7355
        ],
        .ana: [
            .primaryBackground: Color(red: 1.0, green: 1.0, blue: 1.0), // FFFFFF
            .primaryText: Color(red: 0.000, green: 0.122, blue: 0.247), // 001F3F
            // .primaryBackground: Color(red: 0.000, green: 0.122, blue: 0.247), // 001F3F
            // .primaryText: Color(red: 1.000, green: 1.000, blue: 1.000),       // FFFFFF
            .secondaryText: Color(red: 0.529, green: 0.808, blue: 0.922)      // 87CEEB
        ]
    ]

    /// 快取的主題狀態
    private static var cachedTheme: (theme: String, isDark: Bool, timestamp: Date)?

    /// 獲取當前主題狀態（帶快取）
    private static func getCurrentThemeState() -> (isDark: Bool, theme: String) {
        let now = Date()

        // 檢查快取是否有效（1秒內）
        if let cached = cachedTheme,
           now.timeIntervalSince(cached.timestamp) < 1.0 {
            return (cached.isDark, cached.theme)
        }

        // 重新讀取主題設定
        let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather")
        let themeRawValue = groupDefaults?.string(forKey: "themeMode") ?? "light"
        let themeMode = ThemeMode(rawValue: themeRawValue) ?? .light

        let isDarkMode: Bool
        switch themeMode {
        case .dark, .ana:
            isDarkMode = true
        case .light, .muji:
            isDarkMode = false
        case .system:
            if #available(iOS 13.0, *) {
                isDarkMode = UITraitCollection.current.userInterfaceStyle == .dark
            } else {
                isDarkMode = false
            }
        }

        // 只在主題變更時輸出 log
        if cachedTheme?.theme != themeRawValue || cachedTheme?.isDark != isDarkMode {
            Logger.debug("🎨 Widget 主題變更: \(themeRawValue) -> \(isDarkMode ? "深色" : "淺色")模式")
        }

        // 更新快取
        cachedTheme = (themeRawValue, isDarkMode, now)

        return (isDarkMode, themeRawValue)
    }

    /// 獲取主題化顏色
    static func themed(_ colorTheme: ColorTheme) -> Color {
        let (_, themeRawValue) = getCurrentThemeState()
        let themeMode = ThemeMode(rawValue: themeRawValue) ?? .light

        guard let colorMap = themeColors[themeMode],
              let color = colorMap[colorTheme] else {
            return Color.primary
        }

        return color
    }
}
